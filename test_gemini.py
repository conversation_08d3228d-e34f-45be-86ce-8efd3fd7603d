#!/usr/bin/env python3
"""
测试 Gemini API 集成
"""

import asyncio
from google import genai
from google.genai import types

async def test_gemini_api():
    """测试 Gemini API"""
    api_key = "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0"
    
    try:
        # 使用新的客户端初始化方式
        client = genai.Client(api_key=api_key)

        # 测试提示
        prompt = """创建一个简单的HTML动画，要求：
1. 画布尺寸1920x1080
2. 一个红色圆球从左到右移动
3. 使用CSS3动画
4. 动画时长3秒
5. 请返回完整的HTML代码"""

        print("正在调用 Gemini API...")

        # 使用新的API调用方式
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=prompt,
            config=types.GenerateContentConfig(
                system_instruction="你是一个专业的网页动画开发者。请根据用户描述生成HTML+CSS+JS动画代码。",
                temperature=0.7,
                max_output_tokens=2000,
                # 禁用思考功能以提高速度
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )
        )
        
        print("API 调用成功！")
        print("生成的内容:")
        print("-" * 50)
        print(response.text)
        print("-" * 50)
        
        # 保存到文件
        with open("test_animation.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        
        print("HTML 文件已保存为 test_animation.html")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_gemini_api())
