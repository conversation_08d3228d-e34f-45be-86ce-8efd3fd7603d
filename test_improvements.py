#!/usr/bin/env python3
"""
AI动画工作室改进功能测试脚本
测试新增和优化的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from AiAe import *

def test_semantic_parser():
    """测试语义解析引擎"""
    print("=== 测试语义解析引擎 ===")
    
    parser = SemanticParser()
    
    test_descriptions = [
        "从左侧像弹球一样弹跳进入",
        "像火箭一样快速飞向右上角",
        "慢慢淡出并旋转消失",
        "2秒内移动到坐标(800,400)，有弹性效果"
    ]
    
    for desc in test_descriptions:
        print(f"\n描述: {desc}")
        result = parser.parse_description(desc)
        print(f"动作: {result['actions']}")
        print(f"物理效果: {result['physics']}")
        print(f"方向: {result['direction']}")
        print(f"时间特征: {result['timing']}")
        print(f"置信度: {result['confidence']:.2%}")
        if result['duration_hint']:
            print(f"建议时长: {result['duration_hint']}秒")
        if result['position_hint']:
            print(f"目标位置: {result['position_hint']}")

def test_animation_assistant():
    """测试智能动画助手"""
    print("\n=== 测试智能动画助手 ===")
    
    assistant = AnimationAssistant()
    
    test_descriptions = [
        "弹跳进入",
        "火箭式移动",
        "淡出消失",
        "震动效果"
    ]
    
    for desc in test_descriptions:
        print(f"\n描述: {desc}")
        suggestions = assistant.suggest_animations(desc)
        print(f"建议数量: {len(suggestions)}")
        for i, suggestion in enumerate(suggestions[:3]):  # 显示前3个建议
            print(f"  {i+1}. {suggestion['description']} - {suggestion['duration']}s ({suggestion['physics']})")

def test_trajectory_smoothing():
    """测试轨迹平滑算法"""
    print("\n=== 测试轨迹平滑算法 ===")
    
    recorder = TrajectoryRecorder()
    
    # 模拟轨迹点
    test_trajectory = [
        Position(0, 0),
        Position(10, 5),
        Position(25, 15),
        Position(45, 20),
        Position(70, 25),
        Position(100, 30)
    ]
    
    print(f"原始轨迹点数: {len(test_trajectory)}")
    smoothed = recorder.smooth_trajectory(test_trajectory)
    print(f"平滑后轨迹点数: {len(smoothed)}")
    
    print("原始轨迹:")
    for i, point in enumerate(test_trajectory):
        print(f"  {i}: ({point.x:.1f}, {point.y:.1f})")
    
    print("平滑轨迹:")
    for i, point in enumerate(smoothed):
        print(f"  {i}: ({point.x:.1f}, {point.y:.1f})")

def test_animation_templates():
    """测试动画模板系统"""
    print("\n=== 测试动画模板系统 ===")
    
    assistant = AnimationAssistant()
    
    print("可用模板类别:")
    for category in assistant.animation_templates.keys():
        templates = assistant.get_templates_by_category(category)
        print(f"  {category}: {len(templates)} 个模板")
        for template in templates:
            print(f"    - {template['name']}: {template['description']}")

def test_css_generation():
    """测试CSS动画生成"""
    print("\n=== 测试CSS动画生成 ===")
    
    parser = SemanticParser()
    
    # 测试描述
    description = "从左侧弹跳进入，有弹性效果"
    parsed = parser.parse_description(description)
    
    print(f"描述: {description}")
    print(f"解析结果: {parsed}")
    
    # 生成CSS
    css_code = parser.generate_css_animation(
        parsed, 
        "test_element", 
        (0, 100), 
        (200, 100), 
        2.0
    )
    
    print("\n生成的CSS代码:")
    print(css_code)

def main():
    """主测试函数"""
    print("AI动画工作室 - 功能改进测试")
    print("=" * 50)
    
    try:
        test_semantic_parser()
        test_animation_assistant()
        test_trajectory_smoothing()
        test_animation_templates()
        test_css_generation()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
