好的，这是一个满足您要求的HTML动画代码：

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝色方块移动动画</title>
    <style>
        body {
            margin: 0;
            overflow: hidden; /* 防止滚动条出现 */
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh; /* 确保body至少占满视口高度 */
        }

        .canvas-container {
            width: 1920px;
            height: 1080px;
            background-color: #ffffff; /* 画布背景色 */
            position: relative; /* 允许内部元素绝对定位 */
            border: 1px solid #ccc; /* 给画布一个边框方便观察 */
            overflow: hidden; /* 防止方块超出画布 */
        }

        .blue-box {
            width: 50px;
            height: 50px;
            background-color: deepskyblue; /* 蓝色 */
            position: absolute; /* 允许使用top/left进行定位 */
            top: 0;
            left: 0;
            animation: moveBox 2s forwards; /* 动画名称、时长、保持最后一帧状态 */
        }

        /* 定义CSS3关键帧动画 */
        @keyframes moveBox {
            0% {
                top: 0;
                left: 0;
            }
            100% {
                /* 计算右下角位置：画布宽度 - 方块宽度，画布高度 - 方块高度 */
                top: calc(1080px - 50px);
                left: calc(1920px - 50px);
            }
        }
    </style>
</head>
<body>
    <div class="canvas-container">
        <div class="blue-box"></div>
    </div>
</body>
</html>
```