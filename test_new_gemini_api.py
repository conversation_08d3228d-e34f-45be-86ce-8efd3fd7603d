#!/usr/bin/env python3
"""
测试新的Gemini API实现
"""

import asyncio
from google import genai
from google.genai import types

async def test_basic_generation():
    """测试基本的文本生成"""
    api_key = "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0"
    
    try:
        print("=== 测试基本文本生成 ===")
        client = genai.Client(api_key=api_key)
        
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="How does AI work?"
        )
        
        print("基本生成成功:")
        print(response.text[:200] + "...")
        print()
        
    except Exception as e:
        print(f"基本生成失败: {e}")
        return False
    
    return True

async def test_with_system_instruction():
    """测试带系统指令的生成"""
    api_key = "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0"
    
    try:
        print("=== 测试系统指令 ===")
        client = genai.Client(api_key=api_key)
        
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="Hello there",
            config=types.GenerateContentConfig(
                system_instruction="You are a cat. Your name is Neko."
            )
        )
        
        print("系统指令生成成功:")
        print(response.text)
        print()
        
    except Exception as e:
        print(f"系统指令生成失败: {e}")
        return False
    
    return True

async def test_with_thinking_disabled():
    """测试禁用思考功能"""
    api_key = "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0"
    
    try:
        print("=== 测试禁用思考功能 ===")
        client = genai.Client(api_key=api_key)
        
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="Explain quantum computing in simple terms",
            config=types.GenerateContentConfig(
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )
        )
        
        print("禁用思考生成成功:")
        print(response.text[:200] + "...")
        print()
        
    except Exception as e:
        print(f"禁用思考生成失败: {e}")
        return False
    
    return True

async def test_animation_generation():
    """测试动画代码生成"""
    api_key = "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0"
    
    try:
        print("=== 测试动画代码生成 ===")
        client = genai.Client(api_key=api_key)
        
        prompt = """创建一个简单的HTML动画，要求：
1. 画布尺寸1920x1080
2. 一个蓝色方块从左上角移动到右下角
3. 使用CSS3动画
4. 动画时长2秒
5. 请返回完整的HTML代码"""
        
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=prompt,
            config=types.GenerateContentConfig(
                system_instruction="你是一个专业的网页动画开发者。请根据用户描述生成HTML+CSS+JS动画代码。",
                temperature=0.7,
                max_output_tokens=2000,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )
        )
        
        print("动画代码生成成功!")
        print("代码长度:", len(response.text))
        
        # 保存到文件
        with open("test_new_api_animation.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        
        print("HTML文件已保存为 test_new_api_animation.html")
        print()
        
    except Exception as e:
        print(f"动画代码生成失败: {e}")
        return False
    
    return True

async def test_gemini_service_class():
    """测试GeminiService类"""
    try:
        print("=== 测试GeminiService类 ===")
        from AiAe import GeminiService
        
        service = GeminiService("AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0")
        
        prompt = "创建一个简单的红色圆球弹跳动画"
        result = await service.generate_animation_code(prompt)
        
        print("GeminiService生成成功!")
        print("结果长度:", len(result))
        
        # 保存到文件
        with open("test_service_animation.html", "w", encoding="utf-8") as f:
            f.write(result)
        
        print("HTML文件已保存为 test_service_animation.html")
        print()
        
    except Exception as e:
        print(f"GeminiService测试失败: {e}")
        return False
    
    return True

async def main():
    """主测试函数"""
    print("开始测试新的Gemini API实现...")
    print("=" * 60)
    
    tests = [
        ("基本生成", test_basic_generation),
        ("系统指令", test_with_system_instruction),
        ("禁用思考", test_with_thinking_disabled),
        ("动画生成", test_animation_generation),
        ("服务类", test_gemini_service_class)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    print("=" * 60)
    print("测试结果汇总:")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")

if __name__ == "__main__":
    asyncio.run(main())
