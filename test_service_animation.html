```html
<!DOCTYPE html>
<html>
<head>
<title>红色圆球弹跳动画</title>
<style>
  body {
    margin: 0;
    overflow: hidden;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }

  .container {
    width: 1920px;
    height: 1080px;
    position: relative;
    border: 1px solid #ccc;
    background-color: #fff;
    overflow: hidden; /* 确保球不会超出容器 */
  }

  .ball {
    width: 100px;
    height: 100px;
    background-color: red;
    border-radius: 50%;
    position: absolute;
    bottom: 0; /* 初始位置在底部 */
    left: 50%;
    transform: translateX(-50%);
  }
</style>
</head>
<body>

<div class="container">
  <div class="ball" id="bouncingBall"></div>
</div>

<script>
  const ball = document.getElementById('bouncingBall');
  const container = document.querySelector('.container');

  const containerHeight = container.clientHeight;
  const ballHeight = ball.clientHeight;

  // 动画参数
  const duration = 1500; // 每次弹跳的动画时长
  const gravity = 0.002; // 模拟重力，影响加速度
  let initialVelocityY = -1.5; // 初始向上速度
  let damping = 0.85; // 每次弹跳高度衰减系数

  let startTime;
  let animationFrameId;

  function animate(currentTime) {
    if (!startTime) {
      startTime = currentTime;
    }

    const elapsedTime = currentTime - startTime;

    // 模拟自由落体运动
    // s = v0*t + 0.5*a*t^2
    // v = v0 + a*t

    let currentVelocityY = initialVelocityY + gravity * elapsedTime;
    let displacementY = initialVelocityY * elapsedTime + 0.5 * gravity * elapsedTime * elapsedTime;

    let newBottom = -displacementY; // 因为位移是向下为正，而ball的bottom是向上为正，所以取负值

    // 碰撞检测
    if (newBottom <= 0) { // 球碰到地面
      newBottom = 0; // 确保球不会穿透地面
      initialVelocityY = -currentVelocityY * damping; // 反弹，速度衰减
      startTime = currentTime; // 重置动画时间，开始新的弹跳
      
      // 如果反弹速度过小，停止动画
      if (Math.abs(initialVelocityY) < 0.1) {
        ball.style.bottom = '0px';
        cancelAnimationFrame(animationFrameId);
        return;
      }
    }

    ball.style.bottom = `${Math.max(0, newBottom)}px`; // 确保bottom不会是负值

    animationFrameId = requestAnimationFrame(animate);
  }

  // 启动动画
  animationFrameId = requestAnimationFrame(animate);

</script>

</body>
</html>
```