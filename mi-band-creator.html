<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小米手环背景图制作工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }
        
        .preview-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }
        
        .preview-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }
        
        #previewCanvas {
            border: 3px solid #ddd;
            border-radius: 25px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            max-width: 200px;
            max-height: 400px;
        }
        
        .canvas-info {
            margin-top: 15px;
            color: #666;
            font-size: 0.9em;
        }
        
        .controls-section {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .control-group {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }
        
        .control-group h3 {
            color: #333;
            font-size: 1.2em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-weight: 500;
            color: #555;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        
        input[type="text"], textarea, select {
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }
        
        input[type="color"] {
            width: 50px;
            height: 35px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            flex: 1;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #f1f3f4;
            color: #333;
            padding: 12px;
        }
        
        .btn-secondary:hover {
            background: #e8eaed;
        }
        
        .model-selector {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .model-btn {
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
            font-weight: 500;
        }
        
        .model-btn.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .tips {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .tips h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .tips ul {
            color: #424242;
            line-height: 1.6;
        }
        
        .tips li {
            margin-bottom: 5px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 小米手环背景图制作工具</h1>
            <p>智能换行 • 高分辨率 • 视觉特效</p>
        </div>
        
        <div class="main-content">
            <!-- 预览区域 -->
            <div class="preview-section">
                <div class="preview-title">📱 实时预览</div>
                <canvas id="previewCanvas" width="192" height="490"></canvas>
                <div class="canvas-info">
                    <div id="modelInfo">小米手环8 - 192×490px</div>
                    <div style="color: #667eea; margin-top: 5px;">💡 文字会自动换行适配边框</div>
                </div>
                
                <div class="button-group">
                    <button class="btn btn-primary" onclick="downloadImage()">
                        📥 下载高清背景图
                    </button>
                    <button class="btn btn-secondary" onclick="resetSettings()">
                        🔄
                    </button>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="controls-section">
                <!-- 基础设置 -->
                <div class="control-group">
                    <h3>⚙️ 基础设置</h3>
                    
                    <div class="form-group">
                        <label>手环型号</label>
                        <div class="model-selector">
                            <button class="model-btn" onclick="setBandModel('band6')" data-model="band6">小米手环6</button>
                            <button class="model-btn" onclick="setBandModel('band7')" data-model="band7">小米手环7</button>
                            <button class="model-btn active" onclick="setBandModel('band8')" data-model="band8">小米手环8</button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>分辨率倍数: <span id="resolutionValue">3x</span></label>
                        <input type="range" id="resolution" min="1" max="4" value="3" oninput="updateResolution(this.value)">
                        <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666; margin-top: 5px;">
                            <span>标准</span>
                            <span>2x</span>
                            <span>3x</span>
                            <span>4x超清</span>
                        </div>
                    </div>
                </div>
                
                <!-- 文字内容 -->
                <div class="control-group">
                    <h3>📝 文字内容</h3>
                    <div class="form-group">
                        <textarea id="textContent" placeholder="输入要显示的文字&#10;支持手动换行&#10;长文字会自动换行适配边框">今天是美好的一天
继续加油吧！</textarea>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">
                            💡 系统会根据跑道形状智能换行，确保文字完全显示在可见区域内
                        </div>
                    </div>
                </div>
                
                <!-- 文字样式 -->
                <div class="control-group">
                    <h3>🎨 文字样式</h3>
                    
                    <div class="form-group">
                        <label>字体大小: <span id="fontSizeValue">28px</span></label>
                        <input type="range" id="fontSize" min="16" max="60" value="28" oninput="updateFontSize(this.value)">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>文字颜色</label>
                            <input type="color" id="textColor" value="#ffffff" onchange="updatePreview()">
                        </div>
                        <div class="form-group">
                            <label>背景颜色</label>
                            <input type="color" id="backgroundColor" value="#1a1a2e" onchange="updatePreview()">
                        </div>
                    </div>
                </div>
                
                <!-- 文字位置 -->
                <div class="control-group">
                    <h3>📍 文字位置</h3>
                    
                    <div class="form-group">
                        <label>水平位置: <span id="posXValue">50%</span></label>
                        <input type="range" id="positionX" min="20" max="80" value="50" oninput="updatePosition()">
                    </div>
                    
                    <div class="form-group">
                        <label>垂直位置: <span id="posYValue">50%</span></label>
                        <input type="range" id="positionY" min="20" max="80" value="50" oninput="updatePosition()">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 使用说明 -->
        <div class="tips">
            <h3>✨ 使用说明</h3>
            <ul>
                <li><strong>🎯 智能换行：</strong>系统会自动处理长文字，确保不超出显示边框</li>
                <li><strong>📱 高分辨率：</strong>支持1x-4x分辨率，生成超清晰背景图</li>
                <li><strong>💡 使用技巧：</strong>建议使用3x或4x分辨率导出，在手环上显示更清晰</li>
                <li><strong>🎨 推荐搭配：</strong>白色文字+深色背景，或深色文字+亮色背景</li>
            </ul>
        </div>
    </div>
    <script>
        // 全局变量
        let currentBandModel = 'band8';
        let currentResolution = 3;

        // 手环规格
        const bandSpecs = {
            band6: { width: 152, height: 486, name: '小米手环6' },
            band7: { width: 152, height: 486, name: '小米手环7' },
            band8: { width: 192, height: 490, name: '小米手环8' }
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updatePreview();

            // 添加事件监听器
            document.getElementById('textContent').addEventListener('input', updatePreview);
        });

        // 设置手环型号
        function setBandModel(model) {
            currentBandModel = model;

            // 更新按钮状态
            document.querySelectorAll('.model-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-model="${model}"]`).classList.add('active');

            // 更新画布尺寸
            const spec = bandSpecs[model];
            const canvas = document.getElementById('previewCanvas');
            canvas.width = spec.width * currentResolution;
            canvas.height = spec.height * currentResolution;

            // 更新信息显示
            document.getElementById('modelInfo').textContent =
                `${spec.name} - ${spec.width * currentResolution}×${spec.height * currentResolution}px`;

            updatePreview();
        }

        // 更新分辨率
        function updateResolution(value) {
            currentResolution = parseInt(value);
            document.getElementById('resolutionValue').textContent = value + 'x';

            // 更新画布尺寸
            const spec = bandSpecs[currentBandModel];
            const canvas = document.getElementById('previewCanvas');
            canvas.width = spec.width * currentResolution;
            canvas.height = spec.height * currentResolution;

            // 更新信息显示
            document.getElementById('modelInfo').textContent =
                `${spec.name} - ${spec.width * currentResolution}×${spec.height * currentResolution}px`;

            updatePreview();
        }

        // 更新字体大小
        function updateFontSize(value) {
            document.getElementById('fontSizeValue').textContent = value + 'px';
            updatePreview();
        }

        // 更新位置
        function updatePosition() {
            const x = document.getElementById('positionX').value;
            const y = document.getElementById('positionY').value;
            document.getElementById('posXValue').textContent = x + '%';
            document.getElementById('posYValue').textContent = y + '%';
            updatePreview();
        }

        // 智能换行函数
        function wrapText(ctx, text, maxWidth) {
            if (!text || text.trim() === '') return [''];

            const lines = [];
            let currentLine = '';

            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                const testLine = currentLine + char;
                const metrics = ctx.measureText(testLine);

                if (metrics.width > maxWidth && currentLine !== '') {
                    lines.push(currentLine);
                    currentLine = char;
                } else {
                    currentLine = testLine;
                }
            }

            if (currentLine) {
                lines.push(currentLine);
            }

            return lines.length > 0 ? lines : [''];
        }

        // 处理文本换行
        function processText(ctx, text, maxWidth) {
            if (!text) return [''];

            const manualLines = text.split('\n');
            const allLines = [];

            for (let line of manualLines) {
                if (line.trim() === '') {
                    allLines.push('');
                    continue;
                }

                const wrappedLines = wrapText(ctx, line.trim(), maxWidth);
                allLines.push(...wrappedLines);
            }

            return allLines.length > 0 ? allLines : [''];
        }

        // 绘制完整的手环跑道背景
        function drawBackground(ctx, width, height, bgColor) {
            ctx.fillStyle = bgColor;

            const radius = width / 2;

            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 开始绘制完整的跑道形状
            ctx.beginPath();

            // 从左上角开始，顺时针绘制
            // 上边的直线部分
            ctx.moveTo(radius, 0);
            ctx.lineTo(width - radius, 0);

            // 右上角圆弧
            ctx.arc(width - radius, radius, radius, -Math.PI/2, 0);

            // 右边的直线部分
            ctx.lineTo(width, height - radius);

            // 右下角圆弧
            ctx.arc(width - radius, height - radius, radius, 0, Math.PI/2);

            // 下边的直线部分
            ctx.lineTo(radius, height);

            // 左下角圆弧
            ctx.arc(radius, height - radius, radius, Math.PI/2, Math.PI);

            // 左边的直线部分
            ctx.lineTo(0, radius);

            // 左上角圆弧
            ctx.arc(radius, radius, radius, Math.PI, -Math.PI/2);

            // 闭合路径并填充
            ctx.closePath();
            ctx.fill();

            // 添加边框效果（可选）
            ctx.strokeStyle = 'rgba(255,255,255,0.1)';
            ctx.lineWidth = 1;
            ctx.stroke();
        }

        // 更新预览
        function updatePreview() {
            const canvas = document.getElementById('previewCanvas');
            const ctx = canvas.getContext('2d');
            const spec = bandSpecs[currentBandModel];
            const width = spec.width * currentResolution;
            const height = spec.height * currentResolution;

            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 获取设置
            const text = document.getElementById('textContent').value;
            const fontSize = parseInt(document.getElementById('fontSize').value) * currentResolution;
            const textColor = document.getElementById('textColor').value;
            const backgroundColor = document.getElementById('backgroundColor').value;
            const posX = parseInt(document.getElementById('positionX').value);
            const posY = parseInt(document.getElementById('positionY').value);

            // 绘制背景
            drawBackground(ctx, width, height, backgroundColor);

            // 设置字体
            ctx.font = `bold ${fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 计算文字位置
            const textX = (posX / 100) * width;
            const textY = (posY / 100) * height;

            // 计算有效宽度
            const radius = width / 2;
            let effectiveWidth;

            if (textY < radius || textY > height - radius) {
                // 在圆弧区域
                const distFromCenter = Math.abs(textY < radius ? radius - textY : textY - (height - radius));
                const chordWidth = 2 * Math.sqrt(Math.max(0, radius * radius - distFromCenter * distFromCenter));
                effectiveWidth = Math.max(chordWidth * 0.8, width * 0.3);
            } else {
                // 在矩形区域
                effectiveWidth = width * 0.85;
            }

            // 处理文字换行
            const lines = processText(ctx, text, effectiveWidth);

            // 绘制文字
            ctx.fillStyle = textColor;
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2 * currentResolution;

            const lineHeight = fontSize * 1.3;
            const totalHeight = lines.length * lineHeight;
            const startY = textY - totalHeight / 2 + lineHeight / 2;

            lines.forEach((line, index) => {
                const currentY = startY + index * lineHeight;

                // 添加描边效果
                ctx.strokeText(line, textX, currentY);
                ctx.fillText(line, textX, currentY);
            });
        }

        // 下载图片
        function downloadImage() {
            const canvas = document.getElementById('previewCanvas');

            try {
                const link = document.createElement('a');
                link.download = `mi-band-${currentBandModel}-background-${currentResolution}x.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();
            } catch (error) {
                alert('下载失败，请重试');
                console.error('下载失败:', error);
            }
        }

        // 重置设置
        function resetSettings() {
            document.getElementById('textContent').value = '今天是美好的一天\n继续加油吧！';
            document.getElementById('fontSize').value = 28;
            document.getElementById('fontSizeValue').textContent = '28px';
            document.getElementById('textColor').value = '#ffffff';
            document.getElementById('backgroundColor').value = '#1a1a2e';
            document.getElementById('positionX').value = 50;
            document.getElementById('positionY').value = 50;
            document.getElementById('posXValue').textContent = '50%';
            document.getElementById('posYValue').textContent = '50%';

            updatePreview();
        }
    </script>
</body>
</html>
