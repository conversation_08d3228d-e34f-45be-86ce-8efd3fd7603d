#!/usr/bin/env python3
"""
测试动画生成功能
"""

import asyncio
from AiAe import GeminiService, AnimationSegment, Position

async def test_animation_generation():
    """测试动画生成"""
    
    # 创建 Gemini 服务
    gemini_service = GeminiService("AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0")
    
    # 创建测试动画片段
    segment = AnimationSegment(
        start_time=0.0,
        end_time=3.0,
        animation_type="move",
        description="一个红色圆球从左侧像弹球一样弹跳到右侧",
        start_position=Position(100, 400),
        end_position=Position(800, 400),
        physics_type="bounce"
    )
    
    # 构建测试提示
    prompt = f"""请创建一个完整的HTML动画页面，要求如下：
画布尺寸: 1920x1080
元素ID: test_ball

动画时间线详情：
片段 1:
  时间: {segment.start_time:.1f}秒 - {segment.end_time:.1f}秒
  动画类型: {segment.animation_type}
  物理效果: {segment.physics_type}
  描述: {segment.description}
  起始位置: ({segment.start_position.x:.1f}, {segment.start_position.y:.1f})
  结束位置: ({segment.end_position.x:.1f}, {segment.end_position.y:.1f})

技术要求:
1. 使用CSS3动画和/或Web Animation API
2. 动画要符合真实物理规律
3. 确保动画流畅自然
4. 代码要完整可运行
5. 请直接返回完整的HTML代码，不要添加额外解释"""

    try:
        print("正在生成动画...")
        html_code = await gemini_service.generate_animation_code(prompt)
        
        print("动画生成成功！")
        print("=" * 50)
        print(html_code)
        print("=" * 50)
        
        # 保存到文件
        with open("generated_animation.html", "w", encoding="utf-8") as f:
            f.write(html_code)
        
        print("HTML文件已保存为 generated_animation.html")
        
    except Exception as e:
        print(f"生成失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_animation_generation())
