#!/usr/bin/env python3
"""
测试增强的预览功能
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt6.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from AiAe import PreviewWidget

class TestPreviewWindow(QMainWindow):
    """测试预览功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("预览功能测试")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        self.load_sample_btn = QPushButton("加载示例动画")
        self.load_sample_btn.clicked.connect(self.load_sample_animation)
        button_layout.addWidget(self.load_sample_btn)
        
        self.load_complex_btn = QPushButton("加载复杂动画")
        self.load_complex_btn.clicked.connect(self.load_complex_animation)
        button_layout.addWidget(self.load_complex_btn)
        
        self.clear_btn = QPushButton("清空内容")
        self.clear_btn.clicked.connect(self.clear_content)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        # 预览组件
        self.preview_widget = PreviewWidget()
        layout.addWidget(self.preview_widget)
        
        # 连接信号
        self.preview_widget.play_state_changed.connect(self.on_play_state_changed)
        self.preview_widget.time_changed.connect(self.on_time_changed)
        
        # 设置总时长
        self.preview_widget.set_total_duration(5.0)
    
    def on_play_state_changed(self, is_playing: bool):
        """播放状态改变"""
        status = "播放中" if is_playing else "已暂停"
        print(f"播放状态: {status}")
    
    def on_time_changed(self, time: float):
        """时间改变"""
        print(f"当前时间: {time:.2f}s")
    
    def load_sample_animation(self):
        """加载示例动画"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>简单动画示例</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
                    height: 100vh;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    overflow: hidden;
                }
                
                .ball {
                    width: 50px;
                    height: 50px;
                    background: #fff;
                    border-radius: 50%;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                    animation: bounce 2s infinite ease-in-out;
                }
                
                @keyframes bounce {
                    0%, 100% {
                        transform: translateY(0) scale(1);
                    }
                    50% {
                        transform: translateY(-100px) scale(1.1);
                    }
                }
                
                .container {
                    text-align: center;
                }
                
                h1 {
                    color: white;
                    font-family: Arial, sans-serif;
                    margin-bottom: 30px;
                    animation: fadeIn 1s ease-in;
                }
                
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(-20px); }
                    to { opacity: 1; transform: translateY(0); }
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎬 简单弹跳动画</h1>
                <div class="ball"></div>
            </div>
        </body>
        </html>
        """
        
        self.preview_widget.set_html_content(html_content)
        print("已加载简单动画示例")
    
    def load_complex_animation(self):
        """加载复杂动画"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>复杂动画示例</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    background: radial-gradient(circle, #1a1a2e, #16213e, #0f3460);
                    height: 100vh;
                    overflow: hidden;
                    font-family: Arial, sans-serif;
                }
                
                .scene {
                    position: relative;
                    width: 100%;
                    height: 100vh;
                }
                
                .star {
                    position: absolute;
                    background: white;
                    border-radius: 50%;
                    animation: twinkle 3s infinite ease-in-out;
                }
                
                .star:nth-child(1) { top: 20%; left: 10%; width: 2px; height: 2px; animation-delay: 0s; }
                .star:nth-child(2) { top: 30%; left: 80%; width: 3px; height: 3px; animation-delay: 1s; }
                .star:nth-child(3) { top: 60%; left: 20%; width: 2px; height: 2px; animation-delay: 2s; }
                .star:nth-child(4) { top: 80%; left: 70%; width: 3px; height: 3px; animation-delay: 0.5s; }
                .star:nth-child(5) { top: 15%; left: 60%; width: 2px; height: 2px; animation-delay: 1.5s; }
                
                @keyframes twinkle {
                    0%, 100% { opacity: 0.3; transform: scale(1); }
                    50% { opacity: 1; transform: scale(1.5); }
                }
                
                .rocket {
                    position: absolute;
                    bottom: 50px;
                    left: 50px;
                    width: 60px;
                    height: 120px;
                    background: linear-gradient(to top, #ff4757, #ff6b7a);
                    border-radius: 30px 30px 10px 10px;
                    animation: launch 5s infinite ease-in-out;
                }
                
                .rocket::before {
                    content: '';
                    position: absolute;
                    bottom: -20px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    border-left: 15px solid transparent;
                    border-right: 15px solid transparent;
                    border-top: 30px solid #ffa502;
                    animation: flame 0.3s infinite alternate;
                }
                
                @keyframes launch {
                    0% { transform: translateY(0) rotate(0deg); }
                    25% { transform: translateY(-200px) rotate(10deg); }
                    50% { transform: translateY(-400px) rotate(-5deg); }
                    75% { transform: translateY(-200px) rotate(5deg); }
                    100% { transform: translateY(0) rotate(0deg); }
                }
                
                @keyframes flame {
                    0% { border-top-width: 30px; }
                    100% { border-top-width: 40px; }
                }
                
                .title {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: white;
                    text-align: center;
                    animation: glow 2s infinite ease-in-out;
                }
                
                @keyframes glow {
                    0%, 100% { text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px #4ecdc4; }
                    50% { text-shadow: 0 0 20px #fff, 0 0 30px #fff, 0 0 40px #4ecdc4; }
                }
            </style>
        </head>
        <body>
            <div class="scene">
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                <div class="star"></div>
                
                <div class="rocket"></div>
                
                <div class="title">
                    <h1>🚀 太空探索</h1>
                    <p>复杂动画演示</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        self.preview_widget.set_html_content(html_content)
        self.preview_widget.set_total_duration(8.0)  # 更长的动画时间
        print("已加载复杂动画示例")
    
    def clear_content(self):
        """清空内容"""
        self.preview_widget.set_html_content("")
        print("已清空内容")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("预览功能测试")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = TestPreviewWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
