import React, { useState, useRef, useEffect } from 'react';
import { Download, RotateCcw, Palette, Type, Move, Sparkles, Eye } from 'lucide-react';

const MiBandBackgroundCreator = () => {
  const [text, setText] = useState('今天是美好的一天\n继续加油吧！');
  const [fontSize, setFontSize] = useState(28);
  const [textColor, setTextColor] = useState('#ffffff');
  const [backgroundColor, setBackgroundColor] = useState('#1a1a2e');
  const [textPosition, setTextPosition] = useState({ x: 50, y: 50 });
  const [bandModel, setBandModel] = useState('band8');
  const [resolution, setResolution] = useState(3); // 分辨率倍数
  
  // 视觉效果设置
  const [strokeEnabled, setStrokeEnabled] = useState(true);
  const [strokeColor, setStrokeColor] = useState('#000000');
  const [strokeWidth, setStrokeWidth] = useState(2);
  const [shadowEnabled, setShadowEnabled] = useState(true);
  const [shadowBlur, setShadowBlur] = useState(4);
  const [glowEnabled, setGlowEnabled] = useState(false);
  const [gradientEnabled, setGradientEnabled] = useState(false);
  const [gradientColor, setGradientColor] = useState('#ffdd59');
  
  const canvasRef = useRef(null);
  const previewRef = useRef(null);

  // 不同手环型号的基础尺寸
  const baseBandSpecs = {
    band6: { width: 152, height: 486, name: '小米手环6' },
    band7: { width: 152, height: 486, name: '小米手环7' },
    band8: { width: 192, height: 490, name: '小米手环8' }
  };

  // 根据分辨率倍数计算实际尺寸
  const getBandSpec = (model, resolution) => {
    const base = baseBandSpecs[model];
    return {
      ...base,
      width: base.width * resolution,
      height: base.height * resolution
    };
  };

  const currentSpec = getBandSpec(bandModel, resolution);

  // 智能换行函数 - 支持中文字符
  const wrapText = (ctx, text, maxWidth) => {
    if (!text || text.trim() === '') return [''];
    
    const lines = [];
    let currentLine = '';
    
    // 遍历每个字符
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const testLine = currentLine + char;
      
      // 准确测量文字宽度
      const metrics = ctx.measureText(testLine);
      
      if (metrics.width > maxWidth && currentLine !== '') {
        // 当前行已满，开始新行
        lines.push(currentLine);
        currentLine = char;
      } else {
        currentLine = testLine;
      }
    }
    
    // 添加最后一行
    if (currentLine) {
      lines.push(currentLine);
    }
    
    return lines.length > 0 ? lines : [''];
  };

  // 处理手动换行和自动换行
  const processText = (ctx, text, maxWidth) => {
    if (!text) return [''];
    
    const manualLines = text.split('\n');
    const allLines = [];
    
    for (let line of manualLines) {
      if (line.trim() === '') {
        allLines.push('');
        continue;
      }
      
      const wrappedLines = wrapText(ctx, line.trim(), maxWidth);
      allLines.push(...wrappedLines);
    }
    
    return allLines.length > 0 ? allLines : [''];
  };

  // 绘制手环形状背景
  const drawBackground = (ctx, width, height, bgColor) => {
    ctx.fillStyle = bgColor;
    
    const radius = width / 2;
    
    ctx.beginPath();
    ctx.fillRect(0, radius, width, height - radius * 2);
    
    ctx.arc(radius, radius, radius, Math.PI, 0);
    ctx.fill();
    
    ctx.beginPath();
    ctx.arc(radius, height - radius, radius, 0, Math.PI);
    ctx.fill();
  };

  // 创建渐变
  const createGradient = (ctx, x, y, width, height, color1, color2) => {
    const gradient = ctx.createLinearGradient(x, y, x, y + height);
    gradient.addColorStop(0, color1);
    gradient.addColorStop(1, color2);
    return gradient;
  };

  // 绘制带效果的文字
  const drawStyledText = (ctx, lines, x, y, fontSize, options = {}) => {
    const {
      fillColor = '#ffffff',
      strokeEnabled = false,
      strokeColor = '#000000',
      strokeWidth = 2,
      shadowEnabled = false,
      shadowBlur = 4,
      glowEnabled = false,
      gradientEnabled = false,
      gradientColor = '#ffdd59'
    } = options;

    ctx.font = `bold ${fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    const lineHeight = fontSize * 1.3;
    const totalHeight = lines.length * lineHeight;
    const startY = y - totalHeight / 2 + lineHeight / 2;
    
    lines.forEach((line, index) => {
      const currentY = startY + index * lineHeight;
      
      // 设置阴影
      if (shadowEnabled) {
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowBlur = shadowBlur;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
      } else {
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
      }
      
      // 发光效果
      if (glowEnabled) {
        ctx.shadowColor = fillColor;
        ctx.shadowBlur = 15;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.fillStyle = fillColor;
        ctx.fillText(line, x, currentY);
      }
      
      // 描边
      if (strokeEnabled) {
        ctx.strokeStyle = strokeColor;
        ctx.lineWidth = strokeWidth;
        ctx.lineJoin = 'round';
        ctx.miterLimit = 2;
        ctx.strokeText(line, x, currentY);
      }
      
      // 文字填充
      if (gradientEnabled) {
        const textMetrics = ctx.measureText(line);
        const textWidth = textMetrics.width;
        ctx.fillStyle = createGradient(ctx, x - textWidth/2, currentY - fontSize/2, textWidth, fontSize, fillColor, gradientColor);
      } else {
        ctx.fillStyle = fillColor;
      }
      
      ctx.fillText(line, x, currentY);
    });
  };

  // 更新预览
  useEffect(() => {
    if (previewRef.current) {
      const canvas = previewRef.current;
      const ctx = canvas.getContext('2d');
      const { width, height } = currentSpec;
      
      canvas.width = width;
      canvas.height = height;
      
      ctx.clearRect(0, 0, width, height);
      
      // 绘制背景
      drawBackground(ctx, width, height, backgroundColor);
      
      // 设置字体以测量文字
      ctx.font = `bold ${fontSize * resolution}px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif`;
      
      // 计算文字位置
      const textX = (textPosition.x / 100) * width;
      const textY = (textPosition.y / 100) * height;
      
      // 计算跑道形状的有效宽度
      const radius = width / 2;
      
      // 根据文字垂直位置计算可用宽度
      let effectiveWidth;
      if (textY < radius || textY > height - radius) {
        // 在圆弧区域，宽度受限
        const distFromCenter = Math.abs(textY < radius ? radius - textY : textY - (height - radius));
        const chordWidth = 2 * Math.sqrt(Math.max(0, radius * radius - distFromCenter * distFromCenter));
        effectiveWidth = Math.max(chordWidth * 0.8, width * 0.3); // 至少保证30%宽度
      } else {
        // 在矩形区域，使用完整宽度
        effectiveWidth = width * 0.85; // 留出15%边距
      }
      
      // 处理文字换行
      const lines = processText(ctx, text, effectiveWidth);
      
      // 绘制文字
      drawStyledText(ctx, lines, textX, textY, fontSize * resolution, {
        fillColor: textColor,
        strokeEnabled,
        strokeColor,
        strokeWidth: strokeWidth * resolution,
        shadowEnabled,
        shadowBlur: shadowBlur * resolution,
        glowEnabled,
        gradientEnabled,
        gradientColor
      });
    }
  }, [text, fontSize, textColor, backgroundColor, textPosition, bandModel, resolution, 
      strokeEnabled, strokeColor, strokeWidth, shadowEnabled, shadowBlur, 
      glowEnabled, gradientEnabled, gradientColor]);

  // 下载图片
  const downloadImage = () => {
    const canvas = previewRef.current;
    if (!canvas) {
      alert('预览画布未准备好，请稍后重试');
      return;
    }

    try {
      // 创建一个临时 canvas 确保高质量输出
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      const { width, height } = currentSpec;
      
      tempCanvas.width = width;
      tempCanvas.height = height;
      
      // 重新绘制到临时画布
      tempCtx.clearRect(0, 0, width, height);
      
      // 绘制背景
      drawBackground(tempCtx, width, height, backgroundColor);
      
      // 设置字体
      tempCtx.font = `bold ${fontSize * resolution}px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif`;
      
      // 计算文字位置和有效宽度
      const textX = (textPosition.x / 100) * width;
      const textY = (textPosition.y / 100) * height;
      const radius = width / 2;
      
      let effectiveWidth;
      if (textY < radius || textY > height - radius) {
        const distFromCenter = Math.abs(textY < radius ? radius - textY : textY - (height - radius));
        const chordWidth = 2 * Math.sqrt(Math.max(0, radius * radius - distFromCenter * distFromCenter));
        effectiveWidth = Math.max(chordWidth * 0.8, width * 0.3);
      } else {
        effectiveWidth = width * 0.85;
      }
      
      // 处理文字换行
      const lines = processText(tempCtx, text, effectiveWidth);
      
      // 绘制文字
      drawStyledText(tempCtx, lines, textX, textY, fontSize * resolution, {
        fillColor: textColor,
        strokeEnabled,
        strokeColor,
        strokeWidth: strokeWidth * resolution,
        shadowEnabled,
        shadowBlur: shadowBlur * resolution,
        glowEnabled,
        gradientEnabled,
        gradientColor
      });
      
      // 下载图片
      tempCanvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `mi-band-${bandModel}-background-${resolution}x.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        } else {
          alert('生成图片失败，请重试');
        }
      }, 'image/png', 1.0);
      
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败，请重试');
    }
  };

  // 重置设置
  const resetSettings = () => {
    setText('今天是美好的一天\n继续加油吧！');
    setFontSize(28);
    setTextColor('#ffffff');
    setBackgroundColor('#1a1a2e');
    setTextPosition({ x: 50, y: 50 });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl p-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
              <Type className="text-white" size={20} />
            </div>
            高清小米手环背景图制作工具
          </h1>
          <p className="text-gray-600 mb-8">智能换行 • 高分辨率 • 视觉特效</p>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* 左侧：预览区域 */}
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  <Eye size={20} />
                  实时预览
                </h3>
                <div className="flex justify-center">
                  <div className="relative">
                    <canvas
                      ref={previewRef}
                      className="border-2 border-gray-300 rounded-3xl shadow-lg bg-white"
                      style={{
                        maxWidth: '220px',
                        maxHeight: '450px',
                        imageRendering: 'auto'
                      }}
                    />
                    <div className="absolute -inset-4 bg-gradient-to-r from-gray-400 to-gray-600 rounded-[2rem] -z-10 opacity-20"></div>
                  </div>
                </div>
                <div className="text-center mt-4 space-y-1">
                  <p className="text-sm font-medium text-gray-700">
                    {currentSpec.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {currentSpec.width}×{currentSpec.height}px ({resolution}x分辨率)
                  </p>
                  <p className="text-xs text-blue-600">
                    💡 文字会自动换行适配边框
                  </p>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={downloadImage}
                  className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-xl font-medium hover:from-green-600 hover:to-emerald-700 transition-all duration-200 flex items-center justify-center gap-2 shadow-lg"
                >
                  <Download size={20} />
                  下载高清背景图
                </button>
                <button
                  onClick={resetSettings}
                  className="px-4 py-3 bg-gray-200 text-gray-700 rounded-xl hover:bg-gray-300 transition-colors"
                >
                  <RotateCcw size={20} />
                </button>
              </div>
            </div>

            {/* 右侧：控制面板 */}
            <div className="space-y-6">
              {/* 基础设置 */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">基础设置</h3>
                
                {/* 手环型号 */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">手环型号</label>
                  <div className="grid grid-cols-3 gap-2">
                    {Object.entries(baseBandSpecs).map(([key, spec]) => (
                      <button
                        key={key}
                        onClick={() => setBandModel(key)}
                        className={`p-2 rounded-lg text-xs font-medium transition-all ${
                          bandModel === key
                            ? 'bg-blue-500 text-white shadow-lg'
                            : 'bg-white text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        {spec.name}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 分辨率 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    分辨率倍数: {resolution}x ({currentSpec.width}×{currentSpec.height}px)
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="4"
                    value={resolution}
                    onChange={(e) => setResolution(parseInt(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>标准</span>
                    <span>2x</span>
                    <span>3x</span>
                    <span>4x超清</span>
                  </div>
                </div>
              </div>

              {/* 文字内容 */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">文字内容</h3>
                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  placeholder="输入要显示的文字&#10;支持手动换行&#10;长文字会自动换行适配边框"
                  className="w-full h-24 p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-2">
                  💡 系统会根据跑道形状智能换行，确保文字完全显示在可见区域内
                </p>
              </div>

              {/* 文字样式 */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  <Type size={20} />
                  文字样式
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      字体大小: {fontSize}px
                    </label>
                    <input
                      type="range"
                      min="16"
                      max="60"
                      value={fontSize}
                      onChange={(e) => setFontSize(parseInt(e.target.value))}
                      className="w-full"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">文字颜色</label>
                      <div className="flex gap-2">
                        <input
                          type="color"
                          value={textColor}
                          onChange={(e) => setTextColor(e.target.value)}
                          className="w-10 h-8 rounded border"
                        />
                        <input
                          type="text"
                          value={textColor}
                          onChange={(e) => setTextColor(e.target.value)}
                          className="flex-1 px-2 py-1 text-xs border rounded"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">背景颜色</label>
                      <div className="flex gap-2">
                        <input
                          type="color"
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="w-10 h-8 rounded border"
                        />
                        <input
                          type="text"
                          value={backgroundColor}
                          onChange={(e) => setBackgroundColor(e.target.value)}
                          className="flex-1 px-2 py-1 text-xs border rounded"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 视觉特效 */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  <Sparkles size={20} />
                  视觉特效
                </h3>
                
                <div className="space-y-4">
                  {/* 描边效果 */}
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">文字描边</label>
                    <input
                      type="checkbox"
                      checked={strokeEnabled}
                      onChange={(e) => setStrokeEnabled(e.target.checked)}
                      className="w-4 h-4"
                    />
                  </div>
                  {strokeEnabled && (
                    <div className="ml-4 space-y-2">
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="color"
                          value={strokeColor}
                          onChange={(e) => setStrokeColor(e.target.value)}
                          className="w-full h-8 rounded border"
                        />
                        <input
                          type="range"
                          min="1"
                          max="6"
                          value={strokeWidth}
                          onChange={(e) => setStrokeWidth(parseInt(e.target.value))}
                          className="w-full"
                        />
                      </div>
                      <p className="text-xs text-gray-500">描边宽度: {strokeWidth}px</p>
                    </div>
                  )}

                  {/* 阴影效果 */}
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">文字阴影</label>
                    <input
                      type="checkbox"
                      checked={shadowEnabled}
                      onChange={(e) => setShadowEnabled(e.target.checked)}
                      className="w-4 h-4"
                    />
                  </div>
                  {shadowEnabled && (
                    <div className="ml-4">
                      <input
                        type="range"
                        min="1"
                        max="10"
                        value={shadowBlur}
                        onChange={(e) => setShadowBlur(parseInt(e.target.value))}
                        className="w-full"
                      />
                      <p className="text-xs text-gray-500">阴影模糊: {shadowBlur}px</p>
                    </div>
                  )}

                  {/* 发光效果 */}
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">文字发光</label>
                    <input
                      type="checkbox"
                      checked={glowEnabled}
                      onChange={(e) => setGlowEnabled(e.target.checked)}
                      className="w-4 h-4"
                    />
                  </div>

                  {/* 渐变效果 */}
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">渐变文字</label>
                    <input
                      type="checkbox"
                      checked={gradientEnabled}
                      onChange={(e) => setGradientEnabled(e.target.checked)}
                      className="w-4 h-4"
                    />
                  </div>
                  {gradientEnabled && (
                    <div className="ml-4">
                      <input
                        type="color"
                        value={gradientColor}
                        onChange={(e) => setGradientColor(e.target.value)}
                        className="w-full h-8 rounded border"
                      />
                      <p className="text-xs text-gray-500">渐变终止色</p>
                    </div>
                  )}
                </div>
              </div>

              {/* 文字位置 */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  <Move size={20} />
                  文字位置
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      水平位置: {textPosition.x}%
                    </label>
                    <input
                      type="range"
                      min="20"
                      max="80"
                      value={textPosition.x}
                      onChange={(e) => setTextPosition({...textPosition, x: parseInt(e.target.value)})}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      垂直位置: {textPosition.y}%
                    </label>
                    <input
                      type="range"
                      min="20"
                      max="80"
                      value={textPosition.y}
                      onChange={(e) => setTextPosition({...textPosition, y: parseInt(e.target.value)})}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">✨ 新功能说明</h3>
            <div className="text-blue-700 space-y-2 text-sm">
              <p><strong>🎯 智能换行：</strong>系统会自动处理长文字，确保不超出显示边框</p>
              <p><strong>📱 高分辨率：</strong>支持1x-4x分辨率，生成超清晰背景图</p>
              <p><strong>✨ 视觉特效：</strong>描边、阴影、发光、渐变等多种效果让文字更醒目</p>
              <p><strong>🎨 推荐搭配：</strong>白色文字+黑色描边，或深色背景+亮色文字+发光效果</p>
              <p><strong>💡 使用技巧：</strong>建议使用3x或4x分辨率导出，在手环上显示更清晰</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MiBandBackgroundCreator;