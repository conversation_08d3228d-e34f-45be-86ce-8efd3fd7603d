#!/usr/bin/env python3
"""
AI动画工作室 - PyQt6桌面应用程序
主要功能：
1. 智能画布系统（编辑和预览双画布）
2. 时间轴编辑器
3. 素材管理
4. AI动画生成
5. HTML导出
"""

import sys
import json
import os
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import asyncio
import aiohttp
from pathlib import Path
from google import genai
from google.genai import types

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QGraphicsView, QGraphicsScene, QGraphicsItem,
    QGraphicsPixmapItem, QGraphicsProxyWidget, QFrame, QLabel,
    QListWidget, QListWidgetItem, QPushButton, QSlider, QSpinBox,
    QTextEdit, QLineEdit, QComboBox, QGroupBox, QScrollArea,
    QTabWidget, QTreeWidget, QTreeWidgetItem, QProgressBar,
    QMenuBar, QMenu, QToolBar, QStatusBar, QFileDialog,
    QMessageBox, QDialog, QDialogButtonBox, QFormLayout,
    QCheckBox, QSpacerItem, QSizePolicy, QTableWidget, QTableWidgetItem,
    QDoubleSpinBox
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QPointF, QRectF, QSizeF,
    QPropertyAnimation, QEasingCurve, QParallelAnimationGroup,
    QSequentialAnimationGroup, QAbstractAnimation, QObject, QMimeData, QPoint
)
from PyQt6.QtGui import (
    QPixmap, QPainter, QPen, QBrush, QColor, QFont, QAction,
    QIcon, QCursor, QPalette, QLinearGradient, QGradient,
    QKeySequence, QDragEnterEvent, QDropEvent, QDrag
)


# =============================================================================
# 数据模型定义
# =============================================================================

@dataclass
class Position:
    x: float
    y: float


@dataclass
class AssetItem:
    id: str
    name: str
    asset_type: str  # 'image', 'text', 'shape', 'video'
    file_path: str
    width: int
    height: int
    created_at: str


@dataclass
class AnimationSegment:
    """动画片段 - 表示一个时间段内的动画"""
    start_time: float  # 开始时间（秒）
    end_time: float    # 结束时间（秒）
    animation_type: str  # 'appear', 'move', 'transform', 'disappear', 'custom'
    description: str   # 自然语言描述
    start_position: Position
    end_position: Position
    start_scale: float = 1.0
    end_scale: float = 1.0
    start_rotation: float = 0.0
    end_rotation: float = 0.0
    start_opacity: float = 1.0
    end_opacity: float = 1.0
    trajectory_path: List[Position] = None  # 轨迹路径
    physics_type: str = "linear"  # 物理类型：linear, bounce, elastic, spring等
    ai_generated: bool = False

@dataclass
class KeyFrame:
    time: float  # 秒
    frame_type: str  # 'appear', 'move', 'transform', 'disappear', 'custom'
    description: str  # 自然语言描述
    position: Position
    scale: float = 1.0
    rotation: float = 0.0
    opacity: float = 1.0
    ai_generated: bool = False


@dataclass
class StageElement:
    id: str
    asset_id: str
    initial_position: Position
    z_index: int
    keyframes: List[KeyFrame]
    animation_segments: List[AnimationSegment] = None  # 动画片段列表
    trajectory_path: List[Position] = None
    is_recording_trajectory: bool = False
    current_recording_segment: AnimationSegment = None  # 当前正在记录的片段

    def __post_init__(self):
        if self.animation_segments is None:
            self.animation_segments = []


@dataclass
class ProjectData:
    name: str
    canvas_width: int = 1920
    canvas_height: int = 1080
    duration: float = 10.0
    fps: int = 30
    assets: List[AssetItem] = None
    elements: List[StageElement] = None
    ai_config: dict = None

    def __post_init__(self):
        if self.assets is None:
            self.assets = []
        if self.elements is None:
            self.elements = []
        if self.ai_config is None:
            self.ai_config = {
                "openai_api_key": "",
                "claude_api_key": "",
                "gemini_api_key": "AIzaSyCbvMUzcrUDG-L46m0k18eaLdLzFrM5Hy0",
                "preferred_service": "gemini"
            }


# =============================================================================
# AI服务集成
# =============================================================================

class AIService:
    """AI服务基类"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
    
    async def generate_animation_code(self, prompt: str) -> str:
        """生成动画代码"""
        raise NotImplementedError


class OpenAIService(AIService):
    """OpenAI GPT服务"""
    
    async def generate_animation_code(self, prompt: str) -> str:
        async with aiohttp.ClientSession() as session:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "gpt-4",
                "messages": [
                    {
                        "role": "system",
                        "content": """你是一个专业的网页动画开发者。
                        请根据用户描述生成HTML+CSS+JS动画代码。
                        要求：
                        1. 使用CSS3动画或Web Animation API
                        2. 动画要符合真实物理规律
                        3. 代码要完整可运行
                        4. 画布尺寸1920x1080"""
                    },
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 2000,
                "temperature": 0.7
            }
            
            async with session.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=data
            ) as response:
                result = await response.json()
                return result["choices"][0]["message"]["content"]


class ClaudeService(AIService):
    """Claude服务"""

    async def generate_animation_code(self, prompt: str) -> str:
        async with aiohttp.ClientSession() as session:
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json"
            }

            data = {
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 2000,
                "messages": [
                    {
                        "role": "user",
                        "content": f"""请根据以下描述生成HTML动画代码：
                        {prompt}

                        要求：
                        1. 生成完整的HTML文件
                        2. 使用CSS3动画
                        3. 符合物理规律
                        4. 画布尺寸1920x1080"""
                    }
                ]
            }

            async with session.post(
                "https://api.anthropic.com/v1/messages",
                headers=headers,
                json=data
            ) as response:
                result = await response.json()
                return result["content"][0]["text"]


class GeminiService(AIService):
    """Gemini服务 - 使用新的google.genai客户端"""

    def __init__(self, api_key: str):
        super().__init__(api_key)
        try:
            # 使用新的客户端初始化方式
            self.client = genai.Client(api_key=api_key)
        except Exception as e:
            raise Exception(f"Gemini服务初始化失败: {str(e)}")

    async def generate_animation_code(self, prompt: str) -> str:
        """使用Gemini生成动画代码"""
        try:
            # 构建系统指令
            system_instruction = """你是一个专业的网页动画开发者。
请根据用户描述生成HTML+CSS+JS动画代码。

要求：
1. 使用CSS3动画或Web Animation API
2. 动画要符合真实物理规律
3. 代码要完整可运行
4. 画布尺寸1920x1080
5. 请直接返回完整的HTML代码，不要添加额外的解释"""

            # 使用线程池执行同步的Gemini调用，避免阻塞主线程
            import asyncio
            import concurrent.futures

            def sync_generate():
                # 使用新的API调用方式
                return self.client.models.generate_content(
                    model="gemini-2.5-flash",
                    contents=prompt,
                    config=types.GenerateContentConfig(
                        system_instruction=system_instruction,
                        temperature=0.7,
                        max_output_tokens=2000,
                        # 禁用思考功能以提高速度
                        thinking_config=types.ThinkingConfig(thinking_budget=0)
                    )
                )

            # 在线程池中运行同步调用
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(sync_generate)
                response = await asyncio.wrap_future(future)

            if not response or not response.text:
                raise Exception("Gemini API返回空响应")

            return response.text

        except Exception as e:
            raise Exception(f"Gemini API调用失败: {str(e)}")


class AIServiceManager:
    """AI服务管理器"""
    
    def __init__(self):
        self.services = {}
    
    def add_service(self, name: str, service: AIService):
        self.services[name] = service
    
    async def generate_code(self, service_name: str, prompt: str) -> str:
        if service_name not in self.services:
            raise ValueError(f"Service {service_name} not found")
        
        return await self.services[service_name].generate_animation_code(prompt)


# =============================================================================
# 内存管理器
# =============================================================================

class MemoryManager:
    """内存管理器 - 优化大文件和图片的内存使用"""

    def __init__(self):
        self.max_image_size = 2048  # 最大图片尺寸
        self.max_file_size = 50 * 1024 * 1024  # 最大文件大小 50MB
        self.pixmap_cache = {}  # pixmap缓存
        self.cache_limit = 100  # 缓存限制

    def optimize_pixmap(self, file_path: str) -> QPixmap:
        """优化pixmap加载"""
        try:
            # 检查缓存
            if file_path in self.pixmap_cache:
                return self.pixmap_cache[file_path]

            # 检查文件大小
            if os.path.getsize(file_path) > self.max_file_size:
                raise Exception(f"文件过大: {os.path.basename(file_path)}")

            # 加载原始图片
            original_pixmap = QPixmap(file_path)
            if original_pixmap.isNull():
                raise Exception(f"无法加载图片: {os.path.basename(file_path)}")

            # 优化尺寸
            optimized_pixmap = self.resize_if_needed(original_pixmap)

            # 添加到缓存
            self.add_to_cache(file_path, optimized_pixmap)

            return optimized_pixmap

        except Exception as e:
            raise Exception(f"图片优化失败: {str(e)}")

    def resize_if_needed(self, pixmap: QPixmap) -> QPixmap:
        """如果需要则调整图片大小"""
        if pixmap.width() <= self.max_image_size and pixmap.height() <= self.max_image_size:
            return pixmap

        # 保持宽高比缩放
        return pixmap.scaled(
            self.max_image_size, self.max_image_size,
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )

    def add_to_cache(self, key: str, pixmap: QPixmap):
        """添加到缓存"""
        # 如果缓存已满，移除最旧的项
        if len(self.pixmap_cache) >= self.cache_limit:
            oldest_key = next(iter(self.pixmap_cache))
            del self.pixmap_cache[oldest_key]

        self.pixmap_cache[key] = pixmap

    def clear_cache(self):
        """清空缓存"""
        self.pixmap_cache.clear()

    def get_memory_usage_info(self) -> dict:
        """获取内存使用信息"""
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()

        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'cache_size': len(self.pixmap_cache),
            'cache_limit': self.cache_limit
        }


# =============================================================================
# AI生成工作线程
# =============================================================================

class AIGenerationWorker(QThread):
    """AI生成工作线程 - 避免阻塞主界面"""

    # 信号定义
    generation_started = pyqtSignal()
    generation_progress = pyqtSignal(str)  # 进度信息
    generation_completed = pyqtSignal(str)  # 生成完成，返回结果
    generation_failed = pyqtSignal(str)  # 生成失败，返回错误信息

    def __init__(self, ai_service_manager: 'AIServiceManager', service_name: str, prompt: str):
        super().__init__()
        self.ai_service_manager = ai_service_manager
        self.service_name = service_name
        self.prompt = prompt
        self.is_cancelled = False

    def run(self):
        """运行AI生成任务"""
        try:
            self.generation_started.emit()
            self.generation_progress.emit("正在连接AI服务...")

            if self.is_cancelled:
                return

            # 检查服务是否可用
            if self.service_name not in self.ai_service_manager.services:
                self.generation_failed.emit(f"AI服务 {self.service_name} 不可用")
                return

            self.generation_progress.emit("正在生成动画代码...")

            # 创建新的事件循环用于异步调用
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 执行异步生成
                result = loop.run_until_complete(
                    self.ai_service_manager.generate_code(self.service_name, self.prompt)
                )

                if self.is_cancelled:
                    return

                self.generation_progress.emit("生成完成！")
                self.generation_completed.emit(result)

            finally:
                loop.close()

        except Exception as e:
            if not self.is_cancelled:
                self.generation_failed.emit(f"生成失败: {str(e)}")

    def cancel(self):
        """取消生成任务"""
        self.is_cancelled = True
        self.quit()
        self.wait()


# =============================================================================
# 语义解析引擎
# =============================================================================

class SemanticParser:
    """自然语言动画描述语义解析引擎"""

    def __init__(self):
        # 动作词汇库
        self.action_keywords = {
            '移动': ['移动', '滑动', '飞行', '漂移', '滑行', '游走'],
            '旋转': ['旋转', '转动', '自转', '翻转', '转圈'],
            '缩放': ['缩放', '放大', '缩小', '变大', '变小', '伸缩'],
            '淡入': ['淡入', '出现', '显示', '浮现', '渐现'],
            '淡出': ['淡出', '消失', '隐藏', '渐隐', '消散'],
            '弹跳': ['弹跳', '跳跃', '蹦跳', '反弹'],
            '摇摆': ['摇摆', '摆动', '晃动', '摇晃'],
            '震动': ['震动', '颤抖', '抖动', '振动']
        }

        # 物理效果词汇库
        self.physics_keywords = {
            'linear': ['直线', '线性', '匀速', '平滑'],
            'bounce': ['弹性', '弹跳', '反弹', '橡皮球', '皮球'],
            'elastic': ['弹性', '橡皮筋', '弹簧', 'Q弹', '果冻'],
            'ease-in': ['缓入', '慢启动', '渐快'],
            'ease-out': ['缓出', '慢结束', '渐慢'],
            'ease-in-out': ['缓入缓出', '慢启慢停'],
            'spring': ['弹簧', '回弹', '震荡'],
            'gravity': ['重力', '下落', '掉落', '坠落'],
            'rocket': ['火箭', '快速', '急速', '冲刺']
        }

        # 情感词汇库
        self.emotion_keywords = {
            'gentle': ['温和', '轻柔', '优雅', '缓慢', '柔和'],
            'energetic': ['激烈', '强烈', '有力', '快速', '急促'],
            'playful': ['俏皮', '活泼', '可爱', '顽皮'],
            'dramatic': ['戏剧性', '震撼', '夸张', '壮观'],
            'smooth': ['平滑', '流畅', '丝滑', '顺畅']
        }

        # 方向词汇库
        self.direction_keywords = {
            'left': ['左', '左侧', '左边', '向左'],
            'right': ['右', '右侧', '右边', '向右'],
            'up': ['上', '上方', '向上', '上升'],
            'down': ['下', '下方', '向下', '下降'],
            'center': ['中心', '中央', '居中'],
            'diagonal': ['对角', '斜向', '倾斜']
        }

        # 时间词汇库
        self.time_keywords = {
            'fast': ['快', '快速', '迅速', '急速', '瞬间'],
            'slow': ['慢', '缓慢', '徐徐', '慢慢'],
            'instant': ['立即', '瞬间', '马上', '即刻'],
            'gradual': ['逐渐', '渐渐', '慢慢', '一点点']
        }

    def parse_description(self, description: str) -> dict:
        """解析自然语言描述，返回结构化数据"""
        result = {
            'actions': [],
            'physics': 'linear',
            'emotion': 'neutral',
            'direction': None,
            'timing': 'normal',
            'duration_hint': None,
            'position_hint': None,
            'confidence': 0.0
        }

        description_lower = description.lower()
        matched_keywords = 0
        total_keywords = 0

        # 解析动作
        for action, keywords in self.action_keywords.items():
            total_keywords += len(keywords)
            for keyword in keywords:
                if keyword in description_lower:
                    result['actions'].append(action)
                    matched_keywords += 1
                    break

        # 解析物理效果
        for physics, keywords in self.physics_keywords.items():
            total_keywords += len(keywords)
            for keyword in keywords:
                if keyword in description_lower:
                    result['physics'] = physics
                    matched_keywords += 1
                    break

        # 解析情感
        for emotion, keywords in self.emotion_keywords.items():
            total_keywords += len(keywords)
            for keyword in keywords:
                if keyword in description_lower:
                    result['emotion'] = emotion
                    matched_keywords += 1
                    break

        # 解析方向
        for direction, keywords in self.direction_keywords.items():
            total_keywords += len(keywords)
            for keyword in keywords:
                if keyword in description_lower:
                    result['direction'] = direction
                    matched_keywords += 1
                    break

        # 解析时间特征
        for timing, keywords in self.time_keywords.items():
            total_keywords += len(keywords)
            for keyword in keywords:
                if keyword in description_lower:
                    result['timing'] = timing
                    matched_keywords += 1
                    break

        # 计算置信度
        if total_keywords > 0:
            result['confidence'] = matched_keywords / total_keywords

        # 提取时间信息（如"2秒"、"3s"等）
        import re
        time_pattern = r'(\d+(?:\.\d+)?)\s*[秒s]'
        time_match = re.search(time_pattern, description_lower)
        if time_match:
            result['duration_hint'] = float(time_match.group(1))

        # 提取坐标信息（如"(100,200)"、"坐标100,200"等）
        coord_pattern = r'(?:坐标|位置|到)\s*\(?(\d+)\s*[,，]\s*(\d+)\)?'
        coord_match = re.search(coord_pattern, description_lower)
        if coord_match:
            result['position_hint'] = (int(coord_match.group(1)), int(coord_match.group(2)))

        return result

    def generate_css_animation(self, parsed_data: dict, element_id: str,
                             start_pos: tuple, end_pos: tuple, duration: float) -> str:
        """根据解析结果生成CSS动画代码"""

        # 基础动画属性
        animation_name = f"anim_{element_id}_{int(duration*1000)}"

        # 根据物理效果选择缓动函数
        easing_map = {
            'linear': 'linear',
            'bounce': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
            'elastic': 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
            'ease-in': 'ease-in',
            'ease-out': 'ease-out',
            'ease-in-out': 'ease-in-out',
            'spring': 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            'gravity': 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
            'rocket': 'cubic-bezier(0.23, 1, 0.320, 1)'
        }

        easing = easing_map.get(parsed_data['physics'], 'ease-in-out')

        # 根据情感调整动画参数
        emotion_modifiers = {
            'gentle': {'duration_multiplier': 1.5, 'delay': 0.2},
            'energetic': {'duration_multiplier': 0.7, 'delay': 0},
            'playful': {'duration_multiplier': 1.2, 'delay': 0.1},
            'dramatic': {'duration_multiplier': 2.0, 'delay': 0.5},
            'smooth': {'duration_multiplier': 1.0, 'delay': 0}
        }

        emotion = parsed_data.get('emotion', 'neutral')
        modifier = emotion_modifiers.get(emotion, {'duration_multiplier': 1.0, 'delay': 0})

        actual_duration = duration * modifier['duration_multiplier']
        delay = modifier['delay']

        # 生成关键帧
        keyframes = f"""
@keyframes {animation_name} {{
    0% {{
        transform: translate({start_pos[0]}px, {start_pos[1]}px);
        opacity: {'0' if 'fade_in' in parsed_data['actions'] else '1'};
    }}
    100% {{
        transform: translate({end_pos[0]}px, {end_pos[1]}px);
        opacity: {'0' if 'fade_out' in parsed_data['actions'] else '1'};
    }}
}}"""

        # 生成动画CSS
        animation_css = f"""
#{element_id} {{
    animation: {animation_name} {actual_duration:.2f}s {easing} {delay:.2f}s forwards;
}}"""

        return keyframes + "\n" + animation_css


# =============================================================================
# 智能动画生成助手
# =============================================================================

class AnimationAssistant:
    """智能动画生成助手"""

    def __init__(self):
        self.semantic_parser = SemanticParser()
        self.animation_templates = self.load_animation_templates()

    def load_animation_templates(self) -> dict:
        """加载动画模板库"""
        return {
            "entrance": {
                "fade_in": {
                    "description": "淡入出现",
                    "duration": 1.0,
                    "physics": "ease-out",
                    "keyframes": [
                        {"time": 0, "opacity": 0},
                        {"time": 1, "opacity": 1}
                    ]
                },
                "slide_in_left": {
                    "description": "从左侧滑入",
                    "duration": 0.8,
                    "physics": "ease-out",
                    "offset": (-200, 0)
                },
                "bounce_in": {
                    "description": "弹跳进入",
                    "duration": 1.2,
                    "physics": "bounce",
                    "scale_effect": True
                },
                "zoom_in": {
                    "description": "缩放进入",
                    "duration": 0.6,
                    "physics": "ease-out",
                    "keyframes": [
                        {"time": 0, "scale": 0, "opacity": 0},
                        {"time": 0.6, "scale": 1, "opacity": 1}
                    ]
                }
            },
            "movement": {
                "smooth_move": {
                    "description": "平滑移动",
                    "duration": 2.0,
                    "physics": "ease-in-out"
                },
                "rocket_move": {
                    "description": "火箭式移动",
                    "duration": 0.5,
                    "physics": "rocket"
                },
                "elastic_move": {
                    "description": "弹性移动",
                    "duration": 1.5,
                    "physics": "elastic"
                }
            },
            "exit": {
                "fade_out": {
                    "description": "淡出消失",
                    "duration": 1.0,
                    "physics": "ease-in",
                    "keyframes": [
                        {"time": 0, "opacity": 1},
                        {"time": 1, "opacity": 0}
                    ]
                },
                "slide_out_right": {
                    "description": "向右滑出",
                    "duration": 0.8,
                    "physics": "ease-in",
                    "offset": (200, 0)
                },
                "zoom_out": {
                    "description": "缩放消失",
                    "duration": 0.6,
                    "physics": "ease-in",
                    "keyframes": [
                        {"time": 0, "scale": 1, "opacity": 1},
                        {"time": 0.6, "scale": 0, "opacity": 0}
                    ]
                }
            },
            "attention": {
                "pulse": {
                    "description": "脉冲效果",
                    "duration": 1.0,
                    "physics": "ease-in-out",
                    "repeat": True,
                    "keyframes": [
                        {"time": 0, "scale": 1},
                        {"time": 0.5, "scale": 1.1},
                        {"time": 1, "scale": 1}
                    ]
                },
                "shake": {
                    "description": "震动效果",
                    "duration": 0.5,
                    "physics": "linear",
                    "shake_intensity": 10
                },
                "glow": {
                    "description": "发光效果",
                    "duration": 2.0,
                    "physics": "ease-in-out",
                    "glow_color": "#ffff00"
                }
            }
        }

    def suggest_animations(self, description: str, element_type: str = "image") -> List[dict]:
        """根据描述建议合适的动画"""
        parsed = self.semantic_parser.parse_description(description)
        suggestions = []

        # 根据动作类型推荐模板
        for action in parsed['actions']:
            if action in ['淡入', '出现']:
                suggestions.extend(self.get_templates_by_category("entrance"))
            elif action in ['移动', '滑动']:
                suggestions.extend(self.get_templates_by_category("movement"))
            elif action in ['淡出', '消失']:
                suggestions.extend(self.get_templates_by_category("exit"))
            elif action in ['弹跳', '震动']:
                suggestions.extend(self.get_templates_by_category("attention"))

        # 根据物理效果调整建议
        physics_filter = parsed['physics']
        suggestions = [s for s in suggestions if s.get('physics') == physics_filter or physics_filter == 'linear']

        # 根据情感调整建议
        emotion = parsed['emotion']
        if emotion == 'gentle':
            suggestions = [s for s in suggestions if s.get('duration', 1.0) >= 1.0]
        elif emotion == 'energetic':
            suggestions = [s for s in suggestions if s.get('duration', 1.0) <= 1.0]

        return suggestions[:5]  # 返回前5个建议

    def get_templates_by_category(self, category: str) -> List[dict]:
        """获取指定类别的模板"""
        templates = []
        if category in self.animation_templates:
            for name, template in self.animation_templates[category].items():
                template_copy = template.copy()
                template_copy['name'] = name
                template_copy['category'] = category
                templates.append(template_copy)
        return templates

    def generate_animation_sequence(self, elements: List[StageElement],
                                  scene_description: str) -> List[AnimationSegment]:
        """生成完整的动画序列"""
        parsed = self.semantic_parser.parse_description(scene_description)
        sequence = []

        current_time = 0.0

        for i, element in enumerate(elements):
            # 为每个元素生成入场动画
            entrance_template = self.get_best_entrance_template(parsed)
            entrance_segment = self.create_segment_from_template(
                element, entrance_template, current_time
            )
            sequence.append(entrance_segment)

            # 根据描述添加主要动画
            if parsed['actions']:
                main_template = self.get_best_movement_template(parsed)
                main_segment = self.create_segment_from_template(
                    element, main_template, current_time + entrance_template['duration']
                )
                sequence.append(main_segment)

            # 错开元素的入场时间
            current_time += 0.3

        return sequence

    def get_best_entrance_template(self, parsed_data: dict) -> dict:
        """获取最佳入场模板"""
        if 'bounce' in parsed_data['physics']:
            return self.animation_templates['entrance']['bounce_in']
        elif 'fade' in str(parsed_data['actions']):
            return self.animation_templates['entrance']['fade_in']
        else:
            return self.animation_templates['entrance']['slide_in_left']

    def get_best_movement_template(self, parsed_data: dict) -> dict:
        """获取最佳移动模板"""
        physics = parsed_data['physics']
        if physics == 'rocket':
            return self.animation_templates['movement']['rocket_move']
        elif physics == 'elastic':
            return self.animation_templates['movement']['elastic_move']
        else:
            return self.animation_templates['movement']['smooth_move']

    def create_segment_from_template(self, element: StageElement,
                                   template: dict, start_time: float) -> AnimationSegment:
        """从模板创建动画片段"""
        duration = template.get('duration', 1.0)

        segment = AnimationSegment(
            start_time=start_time,
            end_time=start_time + duration,
            animation_type=template.get('category', 'custom'),
            description=template.get('description', ''),
            start_position=element.initial_position,
            end_position=element.initial_position,  # 将根据模板调整
            physics_type=template.get('physics', 'linear')
        )

        # 应用模板特定的变换
        if 'offset' in template:
            offset = template['offset']
            segment.end_position = Position(
                element.initial_position.x + offset[0],
                element.initial_position.y + offset[1]
            )

        return segment


# =============================================================================
# 轨迹记录和管理系统
# =============================================================================

class TrajectoryRecorder:
    """轨迹记录器"""

    def __init__(self):
        self.is_recording = False
        self.current_trajectory = []
        self.start_time = 0.0
        self.sample_interval = 0.1  # 采样间隔（秒）

    def start_recording(self, start_position: Position, start_time: float):
        """开始记录轨迹"""
        self.is_recording = True
        self.current_trajectory = [start_position]
        self.start_time = start_time

    def add_point(self, position: Position, timestamp: float):
        """添加轨迹点"""
        if self.is_recording:
            # 按时间间隔采样
            if not self.current_trajectory or timestamp - self.start_time >= len(self.current_trajectory) * self.sample_interval:
                self.current_trajectory.append(position)

    def stop_recording(self) -> List[Position]:
        """停止记录并返回轨迹"""
        self.is_recording = False
        trajectory = self.current_trajectory.copy()
        self.current_trajectory = []
        return self.smooth_trajectory(trajectory)

    def smooth_trajectory(self, trajectory: List[Position]) -> List[Position]:
        """平滑轨迹"""
        if len(trajectory) < 3:
            return trajectory

        smoothed = [trajectory[0]]  # 保留起点

        # 简单的移动平均平滑
        for i in range(1, len(trajectory) - 1):
            prev_pos = trajectory[i - 1]
            curr_pos = trajectory[i]
            next_pos = trajectory[i + 1]

            # 三点平均
            smooth_x = (prev_pos.x + curr_pos.x + next_pos.x) / 3
            smooth_y = (prev_pos.y + curr_pos.y + next_pos.y) / 3

            smoothed.append(Position(smooth_x, smooth_y))

        smoothed.append(trajectory[-1])  # 保留终点
        return smoothed


class AnimationSegmentManager:
    """动画片段管理器"""

    def __init__(self):
        self.segments = []
        self.current_time = 0.0

    def create_segment(self, start_time: float, end_time: float,
                      animation_type: str, description: str,
                      start_pos: Position, end_pos: Position,
                      trajectory: List[Position] = None) -> AnimationSegment:
        """创建动画片段"""
        segment = AnimationSegment(
            start_time=start_time,
            end_time=end_time,
            animation_type=animation_type,
            description=description,
            start_position=start_pos,
            end_position=end_pos,
            trajectory_path=trajectory
        )
        return segment

    def add_segment(self, segment: AnimationSegment):
        """添加动画片段"""
        self.segments.append(segment)
        # 按时间排序
        self.segments.sort(key=lambda s: s.start_time)

    def get_segments_at_time(self, time: float) -> List[AnimationSegment]:
        """获取指定时间的动画片段"""
        return [s for s in self.segments if s.start_time <= time <= s.end_time]

    def remove_segment(self, segment: AnimationSegment):
        """移除动画片段"""
        if segment in self.segments:
            self.segments.remove(segment)


# =============================================================================
# 自定义图形组件
# =============================================================================

class DraggableStageElement(QGraphicsPixmapItem):
    """可拖拽的舞台元素"""

    def __init__(self, element_data: StageElement, pixmap: QPixmap):
        super().__init__(pixmap)
        self.element_data = element_data
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsMovable)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsSelectable)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemSendsGeometryChanges)

        # 轨迹记录相关
        self.trajectory_recorder = TrajectoryRecorder()
        self.start_position = None
        self.drag_start_time = 0.0

        # 视觉反馈
        self.trajectory_path_item = None  # 轨迹路径显示

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.start_position = Position(self.pos().x(), self.pos().y())

            # 如果启用轨迹记录
            if self.element_data.is_recording_trajectory:
                import time
                self.drag_start_time = time.time()
                self.trajectory_recorder.start_recording(self.start_position, self.drag_start_time)

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        super().mouseMoveEvent(event)

        # 记录轨迹点
        if self.element_data.is_recording_trajectory and self.trajectory_recorder.is_recording:
            import time
            current_pos = Position(self.pos().x(), self.pos().y())
            current_time = time.time()
            self.trajectory_recorder.add_point(current_pos, current_time)

            # 实时显示轨迹
            self.update_trajectory_visualization()

    def mouseReleaseEvent(self, event):
        if self.trajectory_recorder.is_recording:
            # 停止记录并保存轨迹
            trajectory = self.trajectory_recorder.stop_recording()

            # 创建动画片段
            if self.start_position and len(trajectory) > 1:
                end_position = Position(self.pos().x(), self.pos().y())

                # 如果有当前记录的片段，更新它
                if self.element_data.current_recording_segment:
                    segment = self.element_data.current_recording_segment
                    segment.end_position = end_position
                    segment.trajectory_path = trajectory

                    # 添加到动画片段列表
                    self.element_data.animation_segments.append(segment)
                    self.element_data.current_recording_segment = None

        super().mouseReleaseEvent(event)

    def update_trajectory_visualization(self):
        """更新轨迹可视化 - 支持贝塞尔曲线"""
        if not self.trajectory_recorder.current_trajectory:
            return

        # 移除旧的轨迹显示
        if self.trajectory_path_item:
            self.scene().removeItem(self.trajectory_path_item)

        # 创建轨迹路径
        from PyQt6.QtGui import QPainterPath, QPen, QBrush
        from PyQt6.QtWidgets import QGraphicsPathItem, QGraphicsEllipseItem

        trajectory = self.trajectory_recorder.current_trajectory

        if len(trajectory) > 1:
            # 创建平滑的贝塞尔曲线路径
            path = self.create_smooth_path(trajectory)

            # 创建主路径项
            self.trajectory_path_item = QGraphicsPathItem(path)

            # 设置渐变色画笔
            pen = QPen(QColor(100, 200, 255, 180), 3)  # 蓝色半透明
            pen.setStyle(Qt.PenStyle.SolidLine)
            self.trajectory_path_item.setPen(pen)

            # 添加到场景
            self.scene().addItem(self.trajectory_path_item)

            # 添加关键点标记
            self.add_trajectory_markers(trajectory)

    def create_smooth_path(self, trajectory: List[Position]):
        """创建平滑的贝塞尔曲线路径"""
        from PyQt6.QtGui import QPainterPath

        path = QPainterPath()

        if len(trajectory) < 2:
            return path

        # 起始点
        path.moveTo(trajectory[0].x, trajectory[0].y)

        if len(trajectory) == 2:
            # 只有两个点，直接连线
            path.lineTo(trajectory[1].x, trajectory[1].y)
        elif len(trajectory) == 3:
            # 三个点，使用二次贝塞尔曲线
            path.quadTo(
                trajectory[1].x, trajectory[1].y,
                trajectory[2].x, trajectory[2].y
            )
        else:
            # 多个点，使用三次贝塞尔曲线
            for i in range(1, len(trajectory) - 2, 2):
                if i + 2 < len(trajectory):
                    # 三次贝塞尔曲线
                    path.cubicTo(
                        trajectory[i].x, trajectory[i].y,
                        trajectory[i + 1].x, trajectory[i + 1].y,
                        trajectory[i + 2].x, trajectory[i + 2].y
                    )
                else:
                    # 剩余点用二次曲线
                    path.quadTo(
                        trajectory[i].x, trajectory[i].y,
                        trajectory[-1].x, trajectory[-1].y
                    )
                    break

        return path

    def add_trajectory_markers(self, trajectory: List[Position]):
        """添加轨迹关键点标记"""
        from PyQt6.QtWidgets import QGraphicsEllipseItem
        from PyQt6.QtGui import QBrush

        # 起始点标记（绿色）
        start_marker = QGraphicsEllipseItem(
            trajectory[0].x - 4, trajectory[0].y - 4, 8, 8
        )
        start_marker.setBrush(QBrush(QColor(0, 255, 0, 200)))
        start_marker.setPen(QPen(QColor(0, 200, 0), 2))
        self.scene().addItem(start_marker)

        # 结束点标记（红色）
        end_marker = QGraphicsEllipseItem(
            trajectory[-1].x - 4, trajectory[-1].y - 4, 8, 8
        )
        end_marker.setBrush(QBrush(QColor(255, 0, 0, 200)))
        end_marker.setPen(QPen(QColor(200, 0, 0), 2))
        self.scene().addItem(end_marker)

        # 中间关键点标记（黄色）
        for i in range(1, len(trajectory) - 1, max(1, len(trajectory) // 5)):
            marker = QGraphicsEllipseItem(
                trajectory[i].x - 2, trajectory[i].y - 2, 4, 4
            )
            marker.setBrush(QBrush(QColor(255, 255, 0, 150)))
            marker.setPen(QPen(QColor(200, 200, 0), 1))
            self.scene().addItem(marker)


class TimelineWidget(QWidget):
    """时间轴组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.duration = 10.0  # 总时长（秒）
        self.scale = 50  # 像素/秒
        self.tracks = []  # 轨道列表
        self.current_time = 0.0
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 时间标尺
        self.ruler = TimelineRuler(self.duration, self.scale)
        layout.addWidget(self.ruler)
        
        # 轨道容器
        self.track_container = QScrollArea()
        self.track_widget = QWidget()
        self.track_layout = QVBoxLayout(self.track_widget)
        self.track_container.setWidget(self.track_widget)
        self.track_container.setWidgetResizable(True)
        layout.addWidget(self.track_container)
    
    def add_track(self, element: StageElement):
        """添加轨道"""
        track = TimelineTrack(element, self.duration, self.scale)
        self.tracks.append(track)
        self.track_layout.addWidget(track)
    
    def remove_track(self, element_id: str):
        """移除轨道"""
        for i, track in enumerate(self.tracks):
            if track.element.id == element_id:
                self.track_layout.removeWidget(track)
                track.deleteLater()
                del self.tracks[i]
                break
    
    def set_current_time(self, time: float):
        """设置当前时间"""
        self.current_time = time
        self.ruler.set_current_time(time)
        for track in self.tracks:
            track.set_current_time(time)


class TimelineRuler(QWidget):
    """时间轴标尺"""
    
    def __init__(self, duration: float, scale: int):
        super().__init__()
        self.duration = duration
        self.scale = scale  # 像素/秒
        self.current_time = 0.0
        self.setFixedHeight(30)
        self.setMinimumWidth(int(duration * scale))
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.fillRect(self.rect(), QColor(50, 50, 50))
        
        # 绘制时间刻度
        painter.setPen(QPen(QColor(200, 200, 200)))
        font = QFont("Arial", 8)
        painter.setFont(font)
        
        for i in range(int(self.duration) + 1):
            x = int(i * self.scale)
            painter.drawLine(x, 0, x, 20)
            painter.drawText(x + 2, 15, f"{i}s")
        
        # 绘制当前时间指示器
        current_x = int(self.current_time * self.scale)
        painter.setPen(QPen(QColor(255, 0, 0), 2))
        painter.drawLine(int(current_x), 0, int(current_x), int(self.height()))
    
    def set_current_time(self, time: float):
        self.current_time = time
        self.update()


class TimelineTrack(QWidget):
    """时间轴轨道"""
    
    def __init__(self, element: StageElement, duration: float, scale: int):
        super().__init__()
        self.element = element
        self.duration = duration
        self.scale = scale
        self.current_time = 0.0
        self.setFixedHeight(40)
        self.setMinimumWidth(int(duration * scale))
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.fillRect(self.rect(), QColor(70, 70, 70))
        
        # 绘制关键帧
        for keyframe in self.element.keyframes:
            x = keyframe.time * self.scale
            color = QColor(100, 150, 255) if keyframe.ai_generated else QColor(255, 200, 100)
            painter.fillRect(x - 5, 5, 10, 30, color)
            
            # 绘制描述文字
            painter.setPen(QPen(QColor(255, 255, 255)))
            font = QFont("Arial", 7)
            painter.setFont(font)
            painter.drawText(x + 8, 15, keyframe.description[:20] + "...")
        
        # 绘制当前时间指示器
        current_x = int(self.current_time * self.scale)
        painter.setPen(QPen(QColor(255, 0, 0), 2))
        painter.drawLine(current_x, 0, current_x, int(self.height()))
    
    def set_current_time(self, time: float):
        self.current_time = time
        self.update()


class CanvasView(QGraphicsView):
    """画布视图 - 支持实时动画预览"""

    element_selected = pyqtSignal(str)  # 元素选中信号

    def __init__(self, width=1920, height=1080):
        super().__init__()
        self.canvas_width = width
        self.canvas_height = height
        self.setup_scene()
        self.setup_view()
        self.elements = {}  # 存储舞台元素

        # 动画预览相关
        self.preview_timer = QTimer()
        self.preview_timer.timeout.connect(self.update_preview_animation)
        self.preview_start_time = 0
        self.is_previewing = False
        self.current_preview_time = 0.0
    
    def setup_scene(self):
        """设置场景"""
        self.scene = QGraphicsScene()
        self.scene.setSceneRect(0, 0, self.canvas_width, self.canvas_height)
        self.scene.setBackgroundBrush(QBrush(QColor(40, 40, 40)))
        self.setScene(self.scene)
    
    def setup_view(self):
        """设置视图"""
        self.setDragMode(QGraphicsView.DragMode.RubberBandDrag)
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setViewportUpdateMode(QGraphicsView.ViewportUpdateMode.FullViewportUpdate)
        
        # 启用拖拽功能
        self.setAcceptDrops(True)
    
    def add_element(self, element: StageElement, pixmap: QPixmap):
        """添加元素到画布 - 带内存优化"""
        try:
            # 优化大图片的内存使用
            optimized_pixmap = self.optimize_pixmap_for_display(pixmap)

            item = DraggableStageElement(element, optimized_pixmap)
            item.setPos(element.initial_position.x, element.initial_position.y)
            self.scene.addItem(item)
            self.elements[element.id] = item

            # 释放原始pixmap内存（如果进行了优化）
            if optimized_pixmap is not pixmap:
                del pixmap

        except Exception as e:
            QMessageBox.critical(self.parent(), "错误", f"添加元素失败: {str(e)}")

    def optimize_pixmap_for_display(self, pixmap: QPixmap, max_size: int = 2048) -> QPixmap:
        """优化pixmap用于显示 - 减少内存使用"""
        if pixmap.isNull():
            return pixmap

        # 如果图片尺寸超过限制，进行缩放
        if pixmap.width() > max_size or pixmap.height() > max_size:
            # 保持宽高比缩放
            scaled_pixmap = pixmap.scaled(
                max_size, max_size,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            return scaled_pixmap

        return pixmap
    
    def remove_element(self, element_id: str):
        """从画布移除元素"""
        if element_id in self.elements:
            item = self.elements[element_id]
            self.scene.removeItem(item)
            del self.elements[element_id]
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 文件拖拽
            event.acceptProposedAction()
        elif event.mimeData().hasFormat("application/x-asset-id"):
            # 素材拖拽
            event.acceptProposedAction()
        elif event.mimeData().hasText() and event.mimeData().text().startswith("asset:"):
            # 素材拖拽（文本格式）
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        """处理拖拽放置事件"""
        if event.mimeData().hasUrls():
            # 处理文件拖拽
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.svg')):
                    # 发射信号通知主窗口处理文件
                    self.parent().parent().handle_file_drop(file_path, event.pos())

        elif event.mimeData().hasFormat("application/x-asset-id") or (event.mimeData().hasText() and event.mimeData().text().startswith("asset:")):
            # 处理素材拖拽
            if event.mimeData().hasFormat("application/x-asset-id"):
                asset_id = event.mimeData().data("application/x-asset-id").data().decode()
            else:
                asset_id = event.mimeData().text().replace("asset:", "")

            # 通知主窗口处理素材拖拽
            self.parent().parent().handle_asset_drop(asset_id, event.pos())

    def start_preview_animation(self, duration: float = 10.0):
        """开始预览动画"""
        if self.is_previewing:
            return

        self.is_previewing = True
        self.current_preview_time = 0.0
        self.preview_start_time = 0

        # 30fps 预览
        self.preview_timer.start(33)  # 约30fps

    def stop_preview_animation(self):
        """停止预览动画"""
        self.is_previewing = False
        self.preview_timer.stop()

        # 重置所有元素到初始位置
        for element_id, item in self.elements.items():
            if hasattr(item, 'element_data'):
                initial_pos = item.element_data.initial_position
                item.setPos(initial_pos.x, initial_pos.y)
                item.setOpacity(1.0)
                item.setScale(1.0)
                item.setRotation(0.0)

    def update_preview_animation(self):
        """更新预览动画帧"""
        if not self.is_previewing:
            return

        # 更新时间
        self.current_preview_time += 0.033  # 33ms per frame

        # 更新每个元素的动画状态
        for element_id, item in self.elements.items():
            if hasattr(item, 'element_data'):
                self.update_element_animation(item, self.current_preview_time)

    def update_element_animation(self, item: DraggableStageElement, current_time: float):
        """更新单个元素的动画状态"""
        element = item.element_data

        # 检查动画片段
        for segment in element.animation_segments:
            if segment.start_time <= current_time <= segment.end_time:
                # 计算动画进度 (0.0 到 1.0)
                progress = (current_time - segment.start_time) / (segment.end_time - segment.start_time)

                # 应用缓动函数
                eased_progress = self.apply_easing(progress, segment.physics_type)

                # 插值计算当前位置
                start_pos = segment.start_position
                end_pos = segment.end_position

                current_x = start_pos.x + (end_pos.x - start_pos.x) * eased_progress
                current_y = start_pos.y + (end_pos.y - start_pos.y) * eased_progress

                # 应用变换
                item.setPos(current_x, current_y)

                # 应用其他属性变换
                if hasattr(segment, 'start_opacity') and hasattr(segment, 'end_opacity'):
                    opacity = segment.start_opacity + (segment.end_opacity - segment.start_opacity) * eased_progress
                    item.setOpacity(opacity)

                if hasattr(segment, 'start_scale') and hasattr(segment, 'end_scale'):
                    scale = segment.start_scale + (segment.end_scale - segment.start_scale) * eased_progress
                    item.setScale(scale)

                if hasattr(segment, 'start_rotation') and hasattr(segment, 'end_rotation'):
                    rotation = segment.start_rotation + (segment.end_rotation - segment.start_rotation) * eased_progress
                    item.setRotation(rotation)

                break

    def apply_easing(self, progress: float, easing_type: str) -> float:
        """应用缓动函数"""
        import math

        if easing_type == "linear":
            return progress
        elif easing_type == "bounce":
            if progress < 0.5:
                return 2 * progress * progress
            else:
                return 1 - 2 * (1 - progress) * (1 - progress)
        elif easing_type == "elastic":
            if progress == 0 or progress == 1:
                return progress
            return math.pow(2, -10 * progress) * math.sin((progress - 0.1) * 2 * math.pi / 0.4) + 1
        elif easing_type == "ease-in":
            return progress * progress
        elif easing_type == "ease-out":
            return 1 - (1 - progress) * (1 - progress)
        elif easing_type == "ease-in-out":
            if progress < 0.5:
                return 2 * progress * progress
            else:
                return 1 - 2 * (1 - progress) * (1 - progress)
        else:
            return progress  # 默认线性


# =============================================================================
# 自定义拖拽组件
# =============================================================================

class DraggableAssetListWidget(QListWidget):
    """支持拖拽的素材列表组件"""

    asset_dropped_to_canvas = pyqtSignal(AssetItem, QPointF)  # 素材拖拽到画布信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.DragDropMode.DragOnly)
        self.setDefaultDropAction(Qt.DropAction.CopyAction)

    def startDrag(self, supportedActions):
        """开始拖拽"""
        item = self.currentItem()
        if item:
            asset = item.data(Qt.ItemDataRole.UserRole)
            if asset:
                # 创建拖拽对象
                drag = QDrag(self)
                mimeData = QMimeData()

                # 设置拖拽数据
                mimeData.setText(f"asset:{asset.id}")
                mimeData.setData("application/x-asset-id", asset.id.encode())

                # 创建拖拽图标
                if asset.asset_type == 'image' and os.path.exists(asset.file_path):
                    pixmap = QPixmap(asset.file_path)
                    if not pixmap.isNull():
                        # 缩放为拖拽图标大小
                        icon_pixmap = pixmap.scaled(64, 64, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                        drag.setPixmap(icon_pixmap)
                        drag.setHotSpot(QPoint(32, 32))

                drag.setMimeData(mimeData)

                # 执行拖拽
                dropAction = drag.exec(Qt.DropAction.CopyAction)


# =============================================================================
# 主要界面组件
# =============================================================================

class AssetPanel(QWidget):
    """素材面板 - 支持拖拽和双击添加"""

    asset_double_clicked = pyqtSignal(AssetItem)
    asset_drag_started = pyqtSignal(AssetItem)  # 拖拽开始信号

    def __init__(self):
        super().__init__()
        self.assets = []
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("素材库")
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 导入按钮
        import_btn = QPushButton("导入素材")
        import_btn.clicked.connect(self.import_assets)
        layout.addWidget(import_btn)
        
        # 素材列表 - 使用支持拖拽的自定义组件
        self.asset_list = DraggableAssetListWidget()
        self.asset_list.itemDoubleClicked.connect(self.on_asset_double_clicked)
        layout.addWidget(self.asset_list)
        
        # 预览区域
        self.preview_label = QLabel()
        self.preview_label.setFixedSize(200, 150)
        self.preview_label.setStyleSheet("border: 1px solid gray;")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.preview_label)
    
    def import_assets(self):
        """导入素材"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择素材文件", "",
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.svg);;视频文件 (*.mp4 *.avi *.mov)"
        )
        
        for file_path in file_paths:
            self.add_asset(file_path)
    
    def add_asset(self, file_path: str):
        """添加素材 - 带完整错误处理"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "错误", f"文件不存在: {file_path}")
                return False

            # 检查文件是否可读
            if not os.access(file_path, os.R_OK):
                QMessageBox.warning(self, "错误", f"文件无法读取: {file_path}")
                return False

            file_name = os.path.basename(file_path)
            asset_id = f"asset_{len(self.assets)}"

            # 获取文件信息
            if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp')):
                try:
                    pixmap = QPixmap(file_path)
                    if pixmap.isNull():
                        QMessageBox.warning(self, "错误", f"无法加载图片: {file_name}")
                        return False

                    width, height = pixmap.width(), pixmap.height()
                    asset_type = 'image'

                    # 检查图片尺寸是否合理
                    if width <= 0 or height <= 0:
                        QMessageBox.warning(self, "错误", f"图片尺寸无效: {file_name}")
                        return False

                    # 检查文件大小（限制为50MB）
                    file_size = os.path.getsize(file_path)
                    if file_size > 50 * 1024 * 1024:  # 50MB
                        QMessageBox.warning(self, "错误", f"文件过大 (>50MB): {file_name}")
                        return False

                except Exception as e:
                    QMessageBox.critical(self, "错误", f"加载图片失败: {str(e)}")
                    return False
            else:
                width, height = 0, 0  # 视频文件需要其他方法获取尺寸
                asset_type = 'video'

            asset = AssetItem(
                id=asset_id,
                name=file_name,
                asset_type=asset_type,
                file_path=file_path,
                width=width,
                height=height,
                created_at=datetime.now().isoformat()
            )

            self.assets.append(asset)

            # 添加到列表
            item = QListWidgetItem(file_name)
            item.setData(Qt.ItemDataRole.UserRole, asset)
            self.asset_list.addItem(item)

            return True

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加素材失败: {str(e)}")
            return False
    
    def on_asset_double_clicked(self, item: QListWidgetItem):
        """素材双击事件"""
        asset = item.data(Qt.ItemDataRole.UserRole)
        self.asset_double_clicked.emit(asset)


class PropertyPanel(QWidget):
    """属性面板"""

    def __init__(self, main_window=None):
        super().__init__()
        self.current_element = None
        self.main_window = main_window
        self.semantic_parser = SemanticParser()  # 添加语义解析器
        self.animation_assistant = AnimationAssistant()  # 添加动画助手
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("属性面板")
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 基本属性组
        basic_group = QGroupBox("基本属性")
        basic_layout = QFormLayout(basic_group)
        
        self.pos_x_spin = QSpinBox()
        self.pos_x_spin.setRange(-9999, 9999)
        self.pos_y_spin = QSpinBox()
        self.pos_y_spin.setRange(-9999, 9999)
        
        basic_layout.addRow("X坐标:", self.pos_x_spin)
        basic_layout.addRow("Y坐标:", self.pos_y_spin)
        
        self.scale_slider = QSlider(Qt.Orientation.Horizontal)
        self.scale_slider.setRange(10, 500)
        self.scale_slider.setValue(100)
        basic_layout.addRow("缩放:", self.scale_slider)
        
        self.rotation_slider = QSlider(Qt.Orientation.Horizontal)
        self.rotation_slider.setRange(0, 360)
        basic_layout.addRow("旋转:", self.rotation_slider)
        
        layout.addWidget(basic_group)
        
        # 动画属性组
        animation_group = QGroupBox("动画属性")
        animation_layout = QVBoxLayout(animation_group)

        # 轨迹记录
        self.trajectory_checkbox = QCheckBox("记录移动轨迹")
        self.trajectory_checkbox.stateChanged.connect(self.on_trajectory_recording_changed)
        animation_layout.addWidget(self.trajectory_checkbox)

        # 动画片段管理
        segment_label = QLabel("动画片段:")
        animation_layout.addWidget(segment_label)

        self.segment_list = QListWidget()
        self.segment_list.setMaximumHeight(120)
        animation_layout.addWidget(self.segment_list)

        # 动画片段按钮
        segment_btn_layout = QHBoxLayout()
        add_segment_btn = QPushButton("添加片段")
        add_segment_btn.clicked.connect(self.add_animation_segment)
        edit_segment_btn = QPushButton("编辑片段")
        edit_segment_btn.clicked.connect(self.edit_animation_segment)
        segment_btn_layout.addWidget(add_segment_btn)
        segment_btn_layout.addWidget(edit_segment_btn)
        animation_layout.addLayout(segment_btn_layout)

        # 关键帧列表
        keyframe_label = QLabel("关键帧:")
        animation_layout.addWidget(keyframe_label)

        self.keyframe_list = QListWidget()
        self.keyframe_list.setMaximumHeight(100)
        animation_layout.addWidget(self.keyframe_list)

        # 添加关键帧按钮
        add_keyframe_btn = QPushButton("添加关键帧")
        add_keyframe_btn.clicked.connect(self.add_keyframe)
        animation_layout.addWidget(add_keyframe_btn)

        layout.addWidget(animation_group)
        
        # 智能助手组
        assistant_group = QGroupBox("智能动画助手")
        assistant_layout = QVBoxLayout(assistant_group)

        # 快速描述输入
        quick_desc_label = QLabel("快速描述:")
        assistant_layout.addWidget(quick_desc_label)

        self.quick_desc_edit = QLineEdit()
        self.quick_desc_edit.setPlaceholderText("例如: 从左侧弹跳进入")
        self.quick_desc_edit.returnPressed.connect(self.get_smart_suggestions)
        assistant_layout.addWidget(self.quick_desc_edit)

        # 智能建议按钮
        suggestion_btn_layout = QHBoxLayout()
        self.get_suggestions_btn = QPushButton("获取建议")
        self.get_suggestions_btn.clicked.connect(self.get_smart_suggestions)
        self.apply_template_btn = QPushButton("应用模板")
        self.apply_template_btn.clicked.connect(self.apply_selected_template)
        self.apply_template_btn.setEnabled(False)

        suggestion_btn_layout.addWidget(self.get_suggestions_btn)
        suggestion_btn_layout.addWidget(self.apply_template_btn)
        assistant_layout.addLayout(suggestion_btn_layout)

        # 建议列表
        self.suggestions_list = QListWidget()
        self.suggestions_list.setMaximumHeight(120)
        self.suggestions_list.itemSelectionChanged.connect(self.on_suggestion_selected)
        assistant_layout.addWidget(self.suggestions_list)

        layout.addWidget(assistant_group)

        # AI生成组
        ai_group = QGroupBox("AI生成")
        ai_layout = QVBoxLayout(ai_group)

        self.ai_service_combo = QComboBox()
        self.ai_service_combo.addItems(["OpenAI GPT-4", "Claude", "Gemini"])
        ai_layout.addWidget(QLabel("AI服务:"))
        ai_layout.addWidget(self.ai_service_combo)

        self.generate_btn = QPushButton("生成动画代码")
        self.generate_btn.clicked.connect(self.generate_animation)
        ai_layout.addWidget(self.generate_btn)

        layout.addWidget(ai_group)
        
        # 弹性间隙
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))
    
    def set_current_element(self, element: StageElement):
        """设置当前编辑的元素"""
        self.current_element = element
        self.update_ui()

    def update_ui(self):
        """更新界面显示"""
        if not self.current_element:
            return

        # 更新基本属性
        self.pos_x_spin.setValue(int(self.current_element.initial_position.x))
        self.pos_y_spin.setValue(int(self.current_element.initial_position.y))

        # 更新轨迹记录状态
        self.trajectory_checkbox.setChecked(self.current_element.is_recording_trajectory)

        # 更新动画片段列表
        self.segment_list.clear()
        for segment in self.current_element.animation_segments:
            item_text = f"{segment.start_time:.1f}s-{segment.end_time:.1f}s: {segment.description[:30]}..."
            self.segment_list.addItem(item_text)

        # 更新关键帧列表
        self.keyframe_list.clear()
        for kf in self.current_element.keyframes:
            item_text = f"{kf.time}s: {kf.description}"
            self.keyframe_list.addItem(item_text)

    def on_trajectory_recording_changed(self, state):
        """轨迹记录状态改变"""
        if self.current_element:
            self.current_element.is_recording_trajectory = state == Qt.CheckState.Checked.value

    def add_animation_segment(self):
        """添加动画片段"""
        if not self.current_element:
            QMessageBox.warning(self, "警告", "请先选择一个元素")
            return

        dialog = AnimationSegmentDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            segment = dialog.get_segment()

            # 设置起始和结束位置为当前元素位置
            current_pos = self.current_element.initial_position
            segment.start_position = current_pos
            segment.end_position = current_pos

            self.current_element.animation_segments.append(segment)
            self.update_ui()

    def edit_animation_segment(self):
        """编辑动画片段"""
        if not self.current_element:
            return

        current_row = self.segment_list.currentRow()
        if current_row < 0 or current_row >= len(self.current_element.animation_segments):
            QMessageBox.warning(self, "警告", "请先选择一个动画片段")
            return

        segment = self.current_element.animation_segments[current_row]
        dialog = AnimationSegmentDialog(self, segment)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_segment = dialog.get_segment()
            # 保留原有的位置信息
            updated_segment.start_position = segment.start_position
            updated_segment.end_position = segment.end_position
            updated_segment.trajectory_path = segment.trajectory_path

            self.current_element.animation_segments[current_row] = updated_segment
            self.update_ui()
    
    def add_keyframe(self):
        """添加关键帧"""
        if not self.current_element:
            return
        
        dialog = KeyFrameDialog()
        if dialog.exec() == QDialog.DialogCode.Accepted:
            keyframe = dialog.get_keyframe()
            self.current_element.keyframes.append(keyframe)
            self.update_ui()
    
    def generate_animation(self):
        """生成动画代码"""
        if not self.current_element:
            QMessageBox.warning(self, "警告", "请先选择一个元素")
            return

        if not self.main_window:
            QMessageBox.warning(self, "警告", "无法访问主窗口")
            return

        # 构建AI提示
        prompt = self.build_animation_prompt()

        # 获取选择的AI服务
        service_map = {
            "OpenAI GPT-4": "openai",
            "Claude": "claude",
            "Gemini": "gemini"
        }

        selected_service = self.ai_service_combo.currentText()
        service_name = service_map.get(selected_service, "gemini")

        # 检查服务是否可用
        if service_name not in self.main_window.ai_service_manager.services:
            QMessageBox.warning(self, "警告", f"AI服务 {selected_service} 未配置，请在设置中配置API密钥")
            return

        # 启动AI生成
        self.main_window.start_ai_generation(service_name, prompt)
    
    def build_animation_prompt(self) -> str:
        """构建AI动画提示"""
        if not self.current_element:
            return ""

        prompt_parts = [
            "请创建一个完整的HTML动画页面，要求如下：",
            f"画布尺寸: 1920x1080",
            f"元素ID: {self.current_element.id}",
            f"元素初始位置: ({self.current_element.initial_position.x}, {self.current_element.initial_position.y})",
            "",
            "动画时间线详情："
        ]

        # 添加动画片段信息（优先使用）
        if self.current_element.animation_segments:
            for i, segment in enumerate(self.current_element.animation_segments):
                prompt_parts.append(f"片段 {i+1}:")
                prompt_parts.append(f"  时间: {segment.start_time:.1f}秒 - {segment.end_time:.1f}秒")
                prompt_parts.append(f"  动画类型: {segment.animation_type}")
                prompt_parts.append(f"  物理效果: {segment.physics_type}")
                prompt_parts.append(f"  描述: {segment.description}")
                prompt_parts.append(f"  起始位置: ({segment.start_position.x:.1f}, {segment.start_position.y:.1f})")
                prompt_parts.append(f"  结束位置: ({segment.end_position.x:.1f}, {segment.end_position.y:.1f})")

                if segment.trajectory_path:
                    prompt_parts.append(f"  轨迹路径: 已记录 {len(segment.trajectory_path)} 个路径点")

                prompt_parts.append("")

        # 如果没有动画片段，使用关键帧信息
        elif self.current_element.keyframes:
            prompt_parts.append("关键帧信息:")
            for kf in self.current_element.keyframes:
                prompt_parts.append(
                    f"  {kf.time}秒: {kf.description}, "
                    f"位置({kf.position.x}, {kf.position.y}), "
                    f"缩放{kf.scale}, 旋转{kf.rotation}度, 透明度{kf.opacity}"
                )

        # 添加通用要求
        prompt_parts.extend([
            "",
            "技术要求:",
            "1. 使用CSS3动画和/或Web Animation API",
            "2. 动画要符合真实物理规律",
            "3. 确保动画流畅自然",
            "4. 代码要完整可运行",
            "5. 请直接返回完整的HTML代码，不要添加额外解释"
        ])

        return "\n".join(prompt_parts)

    def get_smart_suggestions(self):
        """获取智能动画建议"""
        if not self.current_element:
            QMessageBox.warning(self, "警告", "请先选择一个元素")
            return

        description = self.quick_desc_edit.text().strip()
        if not description:
            QMessageBox.information(self, "提示", "请输入动画描述")
            return

        # 获取建议
        suggestions = self.animation_assistant.suggest_animations(description)

        # 清空并填充建议列表
        self.suggestions_list.clear()

        for suggestion in suggestions:
            item_text = f"{suggestion['description']} ({suggestion['duration']}s, {suggestion['physics']})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, suggestion)
            self.suggestions_list.addItem(item)

        if suggestions:
            self.apply_template_btn.setEnabled(True)
            self.suggestions_list.setCurrentRow(0)  # 选中第一个建议
        else:
            QMessageBox.information(self, "提示", "未找到匹配的动画建议")

    def on_suggestion_selected(self):
        """建议选择改变"""
        current_item = self.suggestions_list.currentItem()
        self.apply_template_btn.setEnabled(current_item is not None)

    def apply_selected_template(self):
        """应用选中的模板"""
        current_item = self.suggestions_list.currentItem()
        if not current_item or not self.current_element:
            return

        template = current_item.data(Qt.ItemDataRole.UserRole)

        # 创建动画片段
        segment = self.animation_assistant.create_segment_from_template(
            self.current_element, template, 0.0
        )

        # 添加到元素
        self.current_element.animation_segments.append(segment)
        self.update_ui()

        # 清空输入
        self.quick_desc_edit.clear()
        self.suggestions_list.clear()
        self.apply_template_btn.setEnabled(False)

        QMessageBox.information(self, "成功", f"已应用动画模板: {template['description']}")


class AnimationSegmentDialog(QDialog):
    """动画片段编辑对话框"""

    def __init__(self, parent=None, segment: AnimationSegment = None):
        super().__init__(parent)
        self.setWindowTitle("编辑动画片段")
        self.setModal(True)
        self.segment = segment
        self.semantic_parser = SemanticParser()
        self.parsed_data = None
        self.setup_ui()
        if segment:
            self.load_segment_data()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 时间设置组
        time_group = QGroupBox("时间设置")
        time_layout = QFormLayout(time_group)

        self.start_time_spin = QDoubleSpinBox()
        self.start_time_spin.setRange(0, 3600)
        self.start_time_spin.setSuffix(" 秒")
        self.start_time_spin.setDecimals(1)
        time_layout.addRow("开始时间:", self.start_time_spin)

        self.end_time_spin = QDoubleSpinBox()
        self.end_time_spin.setRange(0, 3600)
        self.end_time_spin.setSuffix(" 秒")
        self.end_time_spin.setDecimals(1)
        time_layout.addRow("结束时间:", self.end_time_spin)

        layout.addWidget(time_group)

        # 动画类型组
        type_group = QGroupBox("动画类型")
        type_layout = QFormLayout(type_group)

        self.animation_type_combo = QComboBox()
        self.animation_type_combo.addItems([
            "appear", "disappear", "move", "transform", "rotate", "scale", "custom"
        ])
        type_layout.addRow("类型:", self.animation_type_combo)

        self.physics_type_combo = QComboBox()
        self.physics_type_combo.addItems([
            "linear", "bounce", "elastic", "spring", "ease-in", "ease-out", "ease-in-out"
        ])
        type_layout.addRow("物理效果:", self.physics_type_combo)

        layout.addWidget(type_group)

        # 自然语言描述组
        desc_group = QGroupBox("动画描述")
        desc_layout = QVBoxLayout(desc_group)

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText(
            "用自然语言描述动画效果，例如：\n"
            "• 从左侧像弹球一样弹跳进入\n"
            "• 像火箭一样快速飞向右上角\n"
            "• 慢慢淡出并旋转消失\n"
            "• 先放大1.5倍然后缩回原大小"
        )
        self.description_edit.setMaximumHeight(100)
        self.description_edit.textChanged.connect(self.on_description_changed)
        desc_layout.addWidget(self.description_edit)

        # 智能解析结果显示
        self.parse_result_label = QLabel("💡 智能解析: 等待输入描述...")
        self.parse_result_label.setWordWrap(True)
        self.parse_result_label.setStyleSheet("""
            QLabel {
                background-color: rgba(100, 150, 255, 0.1);
                border: 1px solid rgba(100, 150, 255, 0.3);
                border-radius: 4px;
                padding: 8px;
                font-size: 11px;
            }
        """)
        desc_layout.addWidget(self.parse_result_label)

        # 智能建议按钮
        suggestion_layout = QHBoxLayout()
        self.apply_suggestions_btn = QPushButton("应用智能建议")
        self.apply_suggestions_btn.clicked.connect(self.apply_smart_suggestions)
        self.apply_suggestions_btn.setEnabled(False)
        suggestion_layout.addWidget(self.apply_suggestions_btn)

        self.clear_suggestions_btn = QPushButton("清除建议")
        self.clear_suggestions_btn.clicked.connect(self.clear_suggestions)
        suggestion_layout.addWidget(self.clear_suggestions_btn)

        desc_layout.addLayout(suggestion_layout)

        layout.addWidget(desc_group)

        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def load_segment_data(self):
        """加载片段数据到界面"""
        if not self.segment:
            return

        self.start_time_spin.setValue(self.segment.start_time)
        self.end_time_spin.setValue(self.segment.end_time)
        self.animation_type_combo.setCurrentText(self.segment.animation_type)
        self.physics_type_combo.setCurrentText(self.segment.physics_type)
        self.description_edit.setPlainText(self.segment.description)

    def on_description_changed(self):
        """描述文本改变时的智能解析"""
        description = self.description_edit.toPlainText().strip()
        if not description:
            self.parse_result_label.setText("💡 智能解析: 等待输入描述...")
            self.apply_suggestions_btn.setEnabled(False)
            return

        # 进行语义解析
        self.parsed_data = self.semantic_parser.parse_description(description)

        # 显示解析结果
        result_text = "💡 智能解析结果:\n"

        if self.parsed_data['actions']:
            result_text += f"🎬 动作: {', '.join(self.parsed_data['actions'])}\n"

        if self.parsed_data['physics'] != 'linear':
            result_text += f"⚡ 物理效果: {self.parsed_data['physics']}\n"

        if self.parsed_data['direction']:
            result_text += f"🧭 方向: {self.parsed_data['direction']}\n"

        if self.parsed_data['timing'] != 'normal':
            result_text += f"⏱️ 时间特征: {self.parsed_data['timing']}\n"

        if self.parsed_data['duration_hint']:
            result_text += f"⏰ 建议时长: {self.parsed_data['duration_hint']}秒\n"

        if self.parsed_data['position_hint']:
            result_text += f"📍 目标位置: {self.parsed_data['position_hint']}\n"

        result_text += f"🎯 置信度: {self.parsed_data['confidence']:.1%}"

        self.parse_result_label.setText(result_text)
        self.apply_suggestions_btn.setEnabled(self.parsed_data['confidence'] > 0.1)

    def apply_smart_suggestions(self):
        """应用智能建议"""
        if not self.parsed_data:
            return

        # 应用动画类型建议
        if self.parsed_data['actions']:
            primary_action = self.parsed_data['actions'][0]
            type_mapping = {
                '移动': 'move',
                '旋转': 'rotate',
                '缩放': 'scale',
                '淡入': 'appear',
                '淡出': 'disappear',
                '弹跳': 'move',
                '摇摆': 'transform',
                '震动': 'transform'
            }

            suggested_type = type_mapping.get(primary_action, 'custom')
            index = self.animation_type_combo.findText(suggested_type)
            if index >= 0:
                self.animation_type_combo.setCurrentIndex(index)

        # 应用物理效果建议
        physics_type = self.parsed_data['physics']
        index = self.physics_type_combo.findText(physics_type)
        if index >= 0:
            self.physics_type_combo.setCurrentIndex(index)

        # 应用时长建议
        if self.parsed_data['duration_hint']:
            duration = self.parsed_data['duration_hint']
            if self.end_time_spin.value() <= self.start_time_spin.value():
                self.end_time_spin.setValue(self.start_time_spin.value() + duration)

    def clear_suggestions(self):
        """清除建议"""
        self.parse_result_label.setText("💡 智能解析: 等待输入描述...")
        self.apply_suggestions_btn.setEnabled(False)
        self.parsed_data = None

    def get_segment(self) -> AnimationSegment:
        """获取动画片段数据"""
        return AnimationSegment(
            start_time=self.start_time_spin.value(),
            end_time=self.end_time_spin.value(),
            animation_type=self.animation_type_combo.currentText(),
            description=self.description_edit.toPlainText(),
            start_position=Position(0, 0),  # 将在使用时设置
            end_position=Position(0, 0),    # 将在使用时设置
            physics_type=self.physics_type_combo.currentText()
        )


class KeyFrameDialog(QDialog):
    """关键帧编辑对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加关键帧")
        self.setModal(True)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 表单布局
        form_layout = QFormLayout()
        
        self.time_spin = QSpinBox()
        self.time_spin.setRange(0, 60)
        self.time_spin.setSuffix(" 秒")
        form_layout.addRow("时间:", self.time_spin)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["出现", "移动", "变换", "消失", "自定义"])
        form_layout.addRow("类型:", self.type_combo)
        
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("例如: 弹跳出现, 火箭式移动到右上角")
        form_layout.addRow("描述:", self.description_edit)
        
        self.pos_x_spin = QSpinBox()
        self.pos_x_spin.setRange(-9999, 9999)
        form_layout.addRow("X坐标:", self.pos_x_spin)
        
        self.pos_y_spin = QSpinBox()
        self.pos_y_spin.setRange(-9999, 9999)
        form_layout.addRow("Y坐标:", self.pos_y_spin)
        
        layout.addLayout(form_layout)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def get_keyframe(self) -> KeyFrame:
        """获取关键帧数据"""
        type_map = {
            "出现": "appear",
            "移动": "move", 
            "变换": "transform",
            "消失": "disappear",
            "自定义": "custom"
        }
        
        return KeyFrame(
            time=float(self.time_spin.value()),
            frame_type=type_map[self.type_combo.currentText()],
            description=self.description_edit.text(),
            position=Position(self.pos_x_spin.value(), self.pos_y_spin.value())
        )


class PreviewWidget(QWidget):
    """增强预览组件 - 支持多种预览模式和同步播放控制"""

    # 信号定义
    play_state_changed = pyqtSignal(bool)  # 播放状态改变
    time_changed = pyqtSignal(float)  # 时间改变

    def __init__(self):
        super().__init__()

        # 首先检测WebEngine可用性
        self.has_webengine = self.check_webengine_availability()

        # 初始化属性
        self.html_content = ""
        self.is_playing = False
        self.current_time = 0.0
        self.total_duration = 10.0
        self.playback_speed = 1.0

        # 动画控制定时器
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation)

        # 设置UI（现在has_webengine已经可用）
        self.setup_ui()

    def check_webengine_availability(self) -> bool:
        """检测WebEngine是否可用"""
        try:
            from PyQt6.QtWebEngineWidgets import QWebEngineView
            return True
        except ImportError:
            return False

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 状态指示器
        self.setup_status_indicator(layout)

        # 标题和控制按钮
        header_layout = QHBoxLayout()

        title = QLabel("预览")
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(title)

        header_layout.addStretch()

        # 播放速度控制
        speed_label = QLabel("速度:")
        header_layout.addWidget(speed_label)

        self.speed_combo = QComboBox()
        self.speed_combo.addItems(["0.25x", "0.5x", "1x", "2x", "4x"])
        self.speed_combo.setCurrentText("1x")
        self.speed_combo.currentTextChanged.connect(self.on_speed_changed)
        header_layout.addWidget(self.speed_combo)

        # 播放控制按钮
        self.play_btn = QPushButton("▶ 播放")
        self.pause_btn = QPushButton("⏸ 暂停")
        self.stop_btn = QPushButton("⏹ 停止")
        self.refresh_btn = QPushButton("🔄 刷新")

        # 连接信号
        self.play_btn.clicked.connect(self.play_animation)
        self.pause_btn.clicked.connect(self.pause_animation)
        self.stop_btn.clicked.connect(self.stop_animation)
        self.refresh_btn.clicked.connect(self.refresh_preview)

        header_layout.addWidget(self.play_btn)
        header_layout.addWidget(self.pause_btn)
        header_layout.addWidget(self.stop_btn)
        header_layout.addWidget(self.refresh_btn)

        layout.addLayout(header_layout)

        # 时间进度条
        self.setup_progress_bar(layout)

        # 预览模式选择和预览区域
        self.setup_preview_area(layout)

    def setup_status_indicator(self, layout):
        """设置状态指示器"""
        status_layout = QHBoxLayout()

        # WebEngine状态指示
        if self.has_webengine:
            status_icon = QLabel("✅")
            status_text = QLabel("HTML渲染可用")
            status_text.setStyleSheet("color: green; font-weight: bold;")
        else:
            status_icon = QLabel("⚠️")
            status_text = QLabel("仅代码查看模式 (未安装QtWebEngine)")
            status_text.setStyleSheet("color: orange; font-weight: bold;")

        status_layout.addWidget(status_icon)
        status_layout.addWidget(status_text)
        status_layout.addStretch()

        # 添加帮助按钮
        help_btn = QPushButton("💡 帮助")
        help_btn.clicked.connect(self.show_help)
        status_layout.addWidget(help_btn)

        layout.addLayout(status_layout)

    def setup_progress_bar(self, layout):
        """设置时间进度条"""
        progress_layout = QVBoxLayout()

        # 时间标签
        time_layout = QHBoxLayout()
        self.current_time_label = QLabel("00:00")
        self.total_time_label = QLabel("00:10")
        time_layout.addWidget(self.current_time_label)
        time_layout.addStretch()
        time_layout.addWidget(self.total_time_label)
        progress_layout.addLayout(time_layout)

        # 进度条
        self.progress_slider = QSlider(Qt.Orientation.Horizontal)
        self.progress_slider.setMinimum(0)
        self.progress_slider.setMaximum(1000)  # 使用1000作为最大值以获得更精确的控制
        self.progress_slider.setValue(0)
        self.progress_slider.valueChanged.connect(self.on_progress_changed)
        progress_layout.addWidget(self.progress_slider)

        layout.addLayout(progress_layout)

    def setup_preview_area(self, layout):
        """设置预览区域"""
        # 预览模式选择
        mode_layout = QHBoxLayout()
        mode_label = QLabel("预览模式:")
        mode_layout.addWidget(mode_label)

        self.mode_combo = QComboBox()
        if self.has_webengine:
            self.mode_combo.addItems(["HTML渲染", "代码查看", "分屏模式"])
        else:
            self.mode_combo.addItems(["代码查看"])
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        mode_layout.addWidget(self.mode_combo)

        # 添加导出按钮
        export_btn = QPushButton("📁 导出HTML")
        export_btn.clicked.connect(self.export_html)
        mode_layout.addWidget(export_btn)

        # 添加在浏览器中打开按钮
        browser_btn = QPushButton("🌐 浏览器预览")
        browser_btn.clicked.connect(self.open_in_browser)
        mode_layout.addWidget(browser_btn)

        mode_layout.addStretch()
        layout.addLayout(mode_layout)

        # 创建预览区域容器
        self.preview_container = QWidget()
        self.preview_layout = QVBoxLayout(self.preview_container)
        layout.addWidget(self.preview_container)

        # 设置预览组件
        self.setup_preview_components()

    def setup_preview_components(self):
        """设置预览组件"""
        if self.has_webengine:
            # HTML渲染视图
            try:
                from PyQt6.QtWebEngineWidgets import QWebEngineView
                self.web_view = QWebEngineView()
                self.web_view.setHtml(self.get_default_html())
                self.preview_layout.addWidget(self.web_view)
            except ImportError:
                self.has_webengine = False
                self.web_view = None
        else:
            self.web_view = None

        # 代码查看区域
        self.code_view = QTextEdit()
        self.code_view.setReadOnly(True)
        self.code_view.setFont(QFont("Consolas", 10))
        self.setup_code_view_style()

        if not self.has_webengine:
            self.preview_layout.addWidget(self.code_view)
            self.code_view.setPlainText("等待生成动画代码...")
        else:
            self.preview_layout.addWidget(self.code_view)
            self.code_view.hide()  # 默认隐藏

    def setup_code_view_style(self):
        """设置代码查看器样式"""
        self.code_view.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                selection-background-color: #3399ff;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                line-height: 1.4;
            }
        """)

        # 添加右键菜单
        self.code_view.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.code_view.customContextMenuRequested.connect(self.show_code_context_menu)

    def get_default_html(self) -> str:
        """获取默认HTML内容"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>AI动画预览</title>
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    font-family: Arial, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                }
                .message {
                    text-align: center;
                    background: rgba(255,255,255,0.1);
                    padding: 30px;
                    border-radius: 10px;
                    backdrop-filter: blur(10px);
                }
            </style>
        </head>
        <body>
            <div class="message">
                <h2>🎬 AI动画预览</h2>
                <p>等待生成动画代码...</p>
                <p>请在右侧属性面板中描述您想要的动画效果</p>
            </div>
        </body>
        </html>
        """

    def show_help(self):
        """显示帮助信息"""
        help_text = """
        <h3>预览功能说明</h3>
        <p><b>HTML渲染模式:</b> 使用内置浏览器引擎实时预览动画效果</p>
        <p><b>代码查看模式:</b> 查看和编辑生成的HTML代码</p>
        <p><b>分屏模式:</b> 同时显示HTML渲染和代码</p>

        <h3>播放控制</h3>
        <p><b>播放/暂停:</b> 控制动画播放状态</p>
        <p><b>停止:</b> 重置动画到开始位置</p>
        <p><b>速度:</b> 调整播放速度 (0.25x - 4x)</p>
        <p><b>进度条:</b> 拖拽跳转到指定时间</p>

        <h3>导出功能</h3>
        <p><b>导出HTML:</b> 保存动画代码到文件</p>
        <p><b>浏览器预览:</b> 在默认浏览器中打开预览</p>
        """

        QMessageBox.information(self, "预览功能帮助", help_text)

    def on_mode_changed(self, mode: str):
        """预览模式改变"""
        if not self.has_webengine:
            return  # 没有WebEngine时不处理模式切换

        if mode == "HTML渲染":
            self.web_view.show()
            self.code_view.hide()
        elif mode == "代码查看":
            self.web_view.hide()
            self.code_view.show()
        elif mode == "分屏模式":
            self.web_view.show()
            self.code_view.show()
            # 调整布局为水平分割
            self.setup_split_view()

    def setup_split_view(self):
        """设置分屏视图"""
        # 移除现有布局
        while self.preview_layout.count():
            child = self.preview_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(self.web_view)
        splitter.addWidget(self.code_view)
        splitter.setSizes([1, 1])  # 等分

        self.preview_layout.addWidget(splitter)

    def show_code_context_menu(self, position):
        """显示代码右键菜单"""
        menu = self.code_view.createStandardContextMenu()

        # 添加自定义动作
        menu.addSeparator()

        copy_all_action = menu.addAction("📋 复制全部代码")
        copy_all_action.triggered.connect(self.copy_all_code)

        save_action = menu.addAction("💾 保存到文件")
        save_action.triggered.connect(self.export_html)

        browser_action = menu.addAction("🌐 在浏览器中打开")
        browser_action.triggered.connect(self.open_in_browser)

        menu.exec(self.code_view.mapToGlobal(position))

    def copy_all_code(self):
        """复制全部代码到剪贴板"""
        if self.html_content:
            clipboard = QApplication.clipboard()
            clipboard.setText(self.html_content)
            self.show_status_message("代码已复制到剪贴板")

    def show_status_message(self, message: str):
        """显示状态消息"""
        # 这里可以发射信号给主窗口显示状态
        print(f"状态: {message}")  # 临时使用print

    def on_speed_changed(self, speed_text: str):
        """播放速度改变"""
        self.playback_speed = float(speed_text.replace('x', ''))
        if self.is_playing:
            # 重新启动定时器以应用新速度
            self.animation_timer.stop()
            interval = int(33 / self.playback_speed)  # 调整间隔
            self.animation_timer.start(interval)

    def on_progress_changed(self, value: int):
        """进度条改变"""
        if not self.is_playing:  # 只在非播放状态下响应手动拖拽
            progress = value / 1000.0  # 转换为0-1的比例
            self.current_time = progress * self.total_duration
            self.update_time_display()
            self.time_changed.emit(self.current_time)

    def set_total_duration(self, duration: float):
        """设置总时长"""
        self.total_duration = duration
        self.update_time_display()

    def update_time_display(self):
        """更新时间显示"""
        current_min = int(self.current_time // 60)
        current_sec = int(self.current_time % 60)
        total_min = int(self.total_duration // 60)
        total_sec = int(self.total_duration % 60)

        self.current_time_label.setText(f"{current_min:02d}:{current_sec:02d}")
        self.total_time_label.setText(f"{total_min:02d}:{total_sec:02d}")

        # 更新进度条（避免循环触发）
        progress = int((self.current_time / self.total_duration) * 1000) if self.total_duration > 0 else 0
        self.progress_slider.blockSignals(True)
        self.progress_slider.setValue(progress)
        self.progress_slider.blockSignals(False)

    def set_html_content(self, html: str):
        """设置HTML内容"""
        self.html_content = html

        # 更新WebView
        if self.web_view:
            self.web_view.setHtml(html)

        # 更新代码视图
        if hasattr(self, 'code_view'):
            self.code_view.setPlainText(html)

        # 重置播放状态
        self.stop_animation()

    def play_animation(self):
        """播放动画"""
        if not self.is_playing:
            self.is_playing = True
            self.play_btn.setText("⏸ 播放中")
            self.pause_btn.setEnabled(True)

            # 计算定时器间隔
            interval = int(33 / self.playback_speed)  # 基于30fps调整
            self.animation_timer.start(interval)

            # 发射播放状态改变信号
            self.play_state_changed.emit(True)

    def pause_animation(self):
        """暂停动画"""
        if self.is_playing:
            self.is_playing = False
            self.animation_timer.stop()
            self.play_btn.setText("▶ 播放")
            self.pause_btn.setEnabled(False)

            # 发射播放状态改变信号
            self.play_state_changed.emit(False)

    def stop_animation(self):
        """停止动画"""
        self.pause_animation()

        # 重置时间到开始
        self.current_time = 0.0
        self.update_time_display()

        # 重新加载HTML以重置动画
        if self.html_content:
            if self.web_view:
                self.web_view.setHtml(self.html_content)

        # 发射时间改变信号
        self.time_changed.emit(self.current_time)

    def refresh_preview(self):
        """刷新预览"""
        if self.html_content:
            self.set_html_content(self.html_content)
            self.show_status_message("预览已刷新")

    def update_animation(self):
        """更新动画（定时器回调）"""
        if self.is_playing:
            # 更新当前时间
            self.current_time += 0.033 * self.playback_speed

            # 检查是否到达结束
            if self.current_time >= self.total_duration:
                self.current_time = self.total_duration
                self.stop_animation()

            # 更新显示
            self.update_time_display()

            # 发射时间改变信号
            self.time_changed.emit(self.current_time)

    def export_html(self):
        """导出HTML文件"""
        if not self.html_content:
            QMessageBox.warning(self, "警告", "没有可导出的内容")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出HTML文件", "animation.html",
            "HTML文件 (*.html);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.html_content)
                QMessageBox.information(self, "成功", f"HTML文件已保存到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存文件失败:\n{str(e)}")

    def open_in_browser(self):
        """在浏览器中打开预览"""
        if not self.html_content:
            QMessageBox.warning(self, "警告", "没有可预览的内容")
            return

        import tempfile
        import webbrowser

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(self.html_content)
                temp_path = f.name

            # 在浏览器中打开
            webbrowser.open(f'file://{temp_path}')
            self.show_status_message("已在浏览器中打开预览")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开浏览器失败:\n{str(e)}")

    def set_current_time(self, time: float):
        """设置当前时间（外部调用）"""
        self.current_time = max(0, min(time, self.total_duration))
        self.update_time_display()

        # 如果正在播放，暂停一下再继续
        if self.is_playing:
            self.animation_timer.stop()
            self.animation_timer.start(int(33 / self.playback_speed))


# =============================================================================
# AI进度对话框
# =============================================================================

class AIProgressDialog(QDialog):
    """AI生成进度对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("AI代码生成中...")
        self.setModal(True)
        self.setFixedSize(400, 150)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("AI动画代码生成")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 进度标签
        self.status_label = QLabel("准备开始生成...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 不确定进度
        layout.addWidget(self.progress_bar)

        # 详细信息
        self.detail_label = QLabel("")
        self.detail_label.setStyleSheet("color: gray; font-size: 10px;")
        self.detail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.detail_label)

        # 按钮
        button_layout = QHBoxLayout()
        self.cancel_btn = QPushButton("取消生成")
        self.cancel_btn.clicked.connect(self.cancel_generation)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4444;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #cc3333;
            }
        """)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

        # 添加取消信号
        self.is_cancelled = False

    def update_status(self, status: str):
        """更新状态"""
        if not self.is_cancelled:
            self.status_label.setText(status)

            # 根据消息更新详细信息
            if "连接" in status:
                self.detail_label.setText("正在建立与AI服务的连接...")
            elif "生成" in status:
                self.detail_label.setText("AI正在分析您的需求并生成代码...")
            elif "完成" in status:
                self.detail_label.setText("代码生成完成，正在处理结果...")

    def cancel_generation(self):
        """取消生成"""
        if not self.is_cancelled:
            self.is_cancelled = True
            self.status_label.setText("正在取消...")
            self.cancel_btn.setEnabled(False)
            self.reject()


# =============================================================================
# 主窗口
# =============================================================================

class AIAnimationStudio(QMainWindow):
    """AI动画工作室主窗口"""
    
    def __init__(self):
        super().__init__()
        self.project_data = ProjectData("新项目")
        self.ai_service_manager = AIServiceManager()
        self.current_element = None
        self.setup_ui()
        self.setup_ai_services()
        self.setup_shortcuts()
        self.setup_status_bar()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("AI动画工作室")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧面板 - 素材库
        self.asset_panel = AssetPanel()
        self.asset_panel.setMaximumWidth(250)
        self.asset_panel.asset_double_clicked.connect(self.on_asset_added_to_stage)
        main_layout.addWidget(self.asset_panel)
        
        # 中央区域 - 画布和时间轴
        center_layout = QVBoxLayout()
        
        # 画布区域
        canvas_container = QWidget()
        canvas_layout = QVBoxLayout(canvas_container)
        
        # 画布标签页
        self.canvas_tabs = QTabWidget()
        
        # 编辑画布
        self.edit_canvas = CanvasView(1920, 1080)
        self.canvas_tabs.addTab(self.edit_canvas, "编辑画布")
        
        # 预览画布
        self.preview_widget = PreviewWidget()
        self.canvas_tabs.addTab(self.preview_widget, "预览")

        # 连接预览同步信号
        self.setup_preview_sync()
        
        canvas_layout.addWidget(self.canvas_tabs)
        center_layout.addWidget(canvas_container, stretch=3)
        
        # 时间轴
        self.timeline = TimelineWidget()
        center_layout.addWidget(self.timeline, stretch=1)
        
        main_layout.addLayout(center_layout, stretch=3)
        
        # 右侧面板 - 属性面板
        self.property_panel = PropertyPanel(self)
        self.property_panel.setMaximumWidth(300)
        main_layout.addWidget(self.property_panel)
        
        # 设置菜单栏
        self.setup_menu_bar()
        
        # 设置工具栏
        self.setup_toolbar()

    def setup_preview_sync(self):
        """设置预览同步功能"""
        # 连接预览组件的信号
        self.preview_widget.play_state_changed.connect(self.on_preview_play_state_changed)
        self.preview_widget.time_changed.connect(self.on_preview_time_changed)

        # 设置预览组件的总时长
        self.preview_widget.set_total_duration(self.project_data.duration)

    def on_preview_play_state_changed(self, is_playing: bool):
        """预览播放状态改变"""
        if is_playing:
            # 同步启动画布预览
            self.edit_canvas.start_preview_animation(self.project_data.duration)
        else:
            # 同步停止画布预览
            self.edit_canvas.stop_preview_animation()

    def on_preview_time_changed(self, time: float):
        """预览时间改变"""
        # 同步时间轴
        self.timeline.set_current_time(time)

        # 同步时间滑块（如果存在）
        if hasattr(self, 'time_slider'):
            slider_value = int(time * 10)  # 转换为滑块值
            self.time_slider.blockSignals(True)
            self.time_slider.setValue(slider_value)
            self.time_slider.blockSignals(False)

        # 同步时间标签（如果存在）
        if hasattr(self, 'time_label'):
            self.time_label.setText(f"{time:.1f}s")
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        new_action = QAction("新建项目", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        open_action = QAction("打开项目", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        save_action = QAction("保存项目", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        import_action = QAction("导入素材", self)
        import_action.triggered.connect(self.asset_panel.import_assets)
        file_menu.addAction(import_action)
        
        export_action = QAction("导出HTML", self)
        export_action.triggered.connect(self.export_html)
        file_menu.addAction(export_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑")
        
        undo_action = QAction("撤销", self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        edit_menu.addAction(undo_action)
        
        redo_action = QAction("重做", self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        edit_menu.addAction(redo_action)
        
        # AI菜单
        ai_menu = menubar.addMenu("AI")
        
        config_action = QAction("配置AI服务", self)
        config_action.triggered.connect(self.configure_ai_services)
        ai_menu.addAction(config_action)
        
        generate_action = QAction("生成所有动画", self)
        generate_action.triggered.connect(self.generate_all_animations)
        ai_menu.addAction(generate_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        
        # 播放控制
        play_action = QAction("播放", self)
        play_action.triggered.connect(self.play_animation)
        toolbar.addAction(play_action)
        
        pause_action = QAction("暂停", self)
        pause_action.triggered.connect(self.pause_animation)
        toolbar.addAction(pause_action)
        
        stop_action = QAction("停止", self)
        stop_action.triggered.connect(self.stop_animation)
        toolbar.addAction(stop_action)
        
        toolbar.addSeparator()
        
        # 时间控制
        toolbar.addWidget(QLabel("时间: "))
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setMaximum(int(self.project_data.duration * 10))
        self.time_slider.valueChanged.connect(self.on_time_changed)
        toolbar.addWidget(self.time_slider)
        
        self.time_label = QLabel("0.0s")
        toolbar.addWidget(self.time_label)
    
    def setup_ai_services(self):
        """设置AI服务"""
        # 这里需要从配置文件或设置对话框获取API密钥
        if self.project_data.ai_config.get("openai_api_key"):
            openai_service = OpenAIService(self.project_data.ai_config["openai_api_key"])
            self.ai_service_manager.add_service("openai", openai_service)

        if self.project_data.ai_config.get("claude_api_key"):
            claude_service = ClaudeService(self.project_data.ai_config["claude_api_key"])
            self.ai_service_manager.add_service("claude", claude_service)

        if self.project_data.ai_config.get("gemini_api_key"):
            gemini_service = GeminiService(self.project_data.ai_config["gemini_api_key"])
            self.ai_service_manager.add_service("gemini", gemini_service)
    
    def setup_shortcuts(self):
        """设置快捷键"""
        # 空格键播放/暂停
        play_shortcut = QKeySequence(Qt.Key.Key_Space)
        play_action = QAction(self)
        play_action.setShortcut(play_shortcut)
        play_action.triggered.connect(self.toggle_play_pause)
        self.addAction(play_action)

        # Enter键快速添加动画标记
        add_marker_shortcut = QKeySequence(Qt.Key.Key_Return)
        add_marker_action = QAction(self)
        add_marker_action.setShortcut(add_marker_shortcut)
        add_marker_action.triggered.connect(self.quick_add_animation_marker)
        self.addAction(add_marker_action)

        # Delete键删除选中元素
        delete_shortcut = QKeySequence(Qt.Key.Key_Delete)
        delete_action = QAction(self)
        delete_action.setShortcut(delete_shortcut)
        delete_action.triggered.connect(self.delete_selected_element)
        self.addAction(delete_action)

        # Ctrl+D 复制元素
        duplicate_shortcut = QKeySequence(Qt.Modifier.CTRL | Qt.Key.Key_D)
        duplicate_action = QAction(self)
        duplicate_action.setShortcut(duplicate_shortcut)
        duplicate_action.triggered.connect(self.duplicate_selected_element)
        self.addAction(duplicate_action)

        # F5 刷新预览
        refresh_shortcut = QKeySequence(Qt.Key.Key_F5)
        refresh_action = QAction(self)
        refresh_action.setShortcut(refresh_shortcut)
        refresh_action.triggered.connect(self.refresh_preview)
        self.addAction(refresh_action)

        # Ctrl+G 生成动画
        generate_shortcut = QKeySequence(Qt.Modifier.CTRL | Qt.Key.Key_G)
        generate_action = QAction(self)
        generate_action.setShortcut(generate_shortcut)
        generate_action.triggered.connect(self.generate_all_animations)
        self.addAction(generate_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")
    
    def on_asset_added_to_stage(self, asset: AssetItem):
        """素材添加到舞台"""
        try:
            print(f"开始添加素材到舞台: {asset.name}")

            # 创建舞台元素
            element_id = f"element_{len(self.project_data.elements)}"
            element = StageElement(
                id=element_id,
                asset_id=asset.id,
                initial_position=Position(100, 100),
                z_index=len(self.project_data.elements),
                keyframes=[]
            )

            print(f"创建舞台元素: {element_id}")

            # 添加到项目数据
            self.project_data.elements.append(element)

            # 检查文件是否存在
            if not os.path.exists(asset.file_path):
                QMessageBox.warning(self, "文件不存在", f"素材文件不存在: {asset.file_path}")
                return

            # 添加到画布
            pixmap = QPixmap(asset.file_path)
            if pixmap.isNull():
                QMessageBox.warning(self, "加载失败", f"无法加载图片: {asset.file_path}")
                return

            print(f"加载图片成功，原始尺寸: {pixmap.width()}x{pixmap.height()}")

            # 缩放到合适大小
            if pixmap.width() > 200 or pixmap.height() > 200:
                pixmap = pixmap.scaled(200, 200, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                print(f"缩放后尺寸: {pixmap.width()}x{pixmap.height()}")

            self.edit_canvas.add_element(element, pixmap)
            print("已添加到画布")

            # 添加到时间轴
            self.timeline.add_track(element)
            print("已添加到时间轴")

            # 设置为当前元素
            self.current_element = element
            self.property_panel.set_current_element(element)
            print("已设置为当前元素")

            self.status_bar.showMessage(f"已添加素材: {asset.name}")
            print(f"素材添加完成: {asset.name}")

        except Exception as e:
            print(f"添加素材时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"添加素材时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def handle_file_drop(self, file_path: str, position: QPointF):
        """处理文件拖拽"""
        # 添加到素材库
        self.asset_panel.add_asset(file_path)

        # 自动添加到舞台，使用拖拽位置
        if self.asset_panel.assets:
            latest_asset = self.asset_panel.assets[-1]

            # 创建舞台元素，使用拖拽位置
            element_id = f"element_{len(self.project_data.elements)}"
            element = StageElement(
                id=element_id,
                asset_id=latest_asset.id,
                initial_position=Position(position.x(), position.y()),
                z_index=len(self.project_data.elements),
                keyframes=[]
            )

            # 添加到项目数据
            self.project_data.elements.append(element)

            # 添加到画布
            pixmap = QPixmap(latest_asset.file_path)
            if pixmap.width() > 200 or pixmap.height() > 200:
                pixmap = pixmap.scaled(200, 200, Qt.AspectRatioMode.KeepAspectRatio)

            self.edit_canvas.add_element(element, pixmap)

            # 添加到时间轴
            self.timeline.add_track(element)

            # 设置为当前元素
            self.current_element = element
            self.property_panel.set_current_element(element)

            self.status_bar.showMessage(f"已在位置({position.x():.0f}, {position.y():.0f})添加素材: {latest_asset.name}")

    def handle_asset_drop(self, asset_id: str, position: QPointF):
        """处理素材拖拽到画布"""
        try:
            # 查找对应的素材
            asset = None
            for a in self.project_data.assets:
                if a.id == asset_id:
                    asset = a
                    break

            if not asset:
                QMessageBox.warning(self, "错误", "找不到对应的素材")
                return

            # 检查文件是否存在
            if not os.path.exists(asset.file_path):
                QMessageBox.warning(self, "错误", f"素材文件不存在: {asset.file_path}")
                return

            # 创建舞台元素，使用拖拽位置
            element_id = f"element_{len(self.project_data.elements)}"
            element = StageElement(
                id=element_id,
                asset_id=asset.id,
                initial_position=Position(position.x(), position.y()),
                z_index=len(self.project_data.elements),
                keyframes=[]
            )

            # 添加到项目数据
            self.project_data.elements.append(element)

            # 使用内存管理器优化图片
            try:
                pixmap = self.memory_manager.optimize_pixmap(asset.file_path)
                self.edit_canvas.add_element(element, pixmap)

                # 添加到时间轴
                self.timeline.add_track(element)

                # 设置为当前元素
                self.current_element = element
                self.property_panel.set_current_element(element)

                self.status_bar.showMessage(f"已在位置({position.x():.0f}, {position.y():.0f})添加素材: {asset.name}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载素材失败: {str(e)}")
                # 从项目数据中移除失败的元素
                if element in self.project_data.elements:
                    self.project_data.elements.remove(element)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理素材拖拽失败: {str(e)}")

    def configure_ai_services(self):
        """配置AI服务"""
        dialog = AIConfigDialog(self.project_data.ai_config)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.project_data.ai_config = dialog.get_config()
            self.setup_ai_services()
            self.status_bar.showMessage("AI服务配置已更新")
    
    def generate_all_animations(self):
        """生成所有动画"""
        if not self.project_data.elements:
            QMessageBox.information(self, "提示", "请先添加一些元素到舞台")
            return
        
        # 构建综合提示
        prompt = self.build_complete_animation_prompt()
        
        # 选择AI服务
        service_name = self.project_data.ai_config.get("preferred_service", "openai")
        
        if service_name not in self.ai_service_manager.services:
            QMessageBox.warning(self, "警告", f"AI服务 {service_name} 未配置")
            return
        
        # 启动AI生成
        self.start_ai_generation(service_name, prompt)
    
    def build_complete_animation_prompt(self) -> str:
        """构建完整动画提示"""
        prompt_parts = [
            "请创建一个完整的HTML动画页面，包含以下要求：",
            f"画布尺寸: {self.project_data.canvas_width}x{self.project_data.canvas_height}",
            f"总时长: {self.project_data.duration}秒",
            f"帧率: {self.project_data.fps}fps",
            "",
            "技术要求:",
            "1. 使用CSS3动画和/或Web Animation API",
            "2. 所有动画要符合真实物理规律",
            "3. 确保动画流畅自然",
            "4. 代码要完整可运行",
            "5. 请直接返回完整的HTML代码，不要添加额外解释",
            "",
            "元素和动画详情："
        ]

        for element in self.project_data.elements:
            # 找到对应的素材信息
            asset = next((a for a in self.project_data.assets if a.id == element.asset_id), None)
            if asset:
                prompt_parts.append(f"元素 {element.id} ({asset.name}):")
                prompt_parts.append(f"  初始位置: ({element.initial_position.x:.1f}, {element.initial_position.y:.1f})")
                prompt_parts.append(f"  层级: {element.z_index}")

                # 优先使用动画片段
                if element.animation_segments:
                    prompt_parts.append("  动画片段:")
                    for i, segment in enumerate(element.animation_segments):
                        prompt_parts.append(f"    片段 {i+1}:")
                        prompt_parts.append(f"      时间: {segment.start_time:.1f}s - {segment.end_time:.1f}s")
                        prompt_parts.append(f"      类型: {segment.animation_type}")
                        prompt_parts.append(f"      物理效果: {segment.physics_type}")
                        prompt_parts.append(f"      描述: {segment.description}")
                        prompt_parts.append(f"      起始位置: ({segment.start_position.x:.1f}, {segment.start_position.y:.1f})")
                        prompt_parts.append(f"      结束位置: ({segment.end_position.x:.1f}, {segment.end_position.y:.1f})")

                        if segment.trajectory_path:
                            prompt_parts.append(f"      轨迹: 已记录 {len(segment.trajectory_path)} 个路径点")

                # 如果没有动画片段，使用关键帧
                elif element.keyframes:
                    prompt_parts.append("  关键帧:")
                    for kf in element.keyframes:
                        prompt_parts.append(f"    {kf.time}s: {kf.description}")
                        prompt_parts.append(f"      位置: ({kf.position.x}, {kf.position.y})")
                        if kf.scale != 1.0:
                            prompt_parts.append(f"      缩放: {kf.scale}")
                        if kf.rotation != 0.0:
                            prompt_parts.append(f"      旋转: {kf.rotation}度")
                        if kf.opacity != 1.0:
                            prompt_parts.append(f"      透明度: {kf.opacity}")

                prompt_parts.append("")
        
        return "\n".join(prompt_parts)
    
    def start_ai_generation(self, service_name: str, prompt: str):
        """启动AI生成 - 支持取消功能"""
        try:
            # 创建进度对话框
            progress_dialog = AIProgressDialog(self)

            # 创建工作线程
            self.ai_worker = AIGenerationWorker(self.ai_service_manager, service_name, prompt)

            # 连接信号
            self.ai_worker.generation_started.connect(lambda: progress_dialog.update_status("正在启动AI服务..."))
            self.ai_worker.generation_progress.connect(progress_dialog.update_status)
            self.ai_worker.generation_completed.connect(self.on_ai_generation_completed)
            self.ai_worker.generation_completed.connect(progress_dialog.accept)  # 关闭对话框
            self.ai_worker.generation_failed.connect(self.on_ai_generation_failed)
            self.ai_worker.generation_failed.connect(progress_dialog.reject)  # 关闭对话框

            # 连接取消信号
            progress_dialog.finished.connect(self.on_ai_generation_cancelled)

            # 启动线程
            self.ai_worker.start()

            # 显示进度对话框（模态）
            result = progress_dialog.exec()

            # 如果对话框被取消，停止工作线程
            if result == QDialog.DialogCode.Rejected and hasattr(self, 'ai_worker'):
                self.ai_worker.cancel()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动AI生成失败: {str(e)}")

    def on_ai_generation_cancelled(self):
        """AI生成被取消"""
        if hasattr(self, 'ai_worker') and self.ai_worker.isRunning():
            self.ai_worker.cancel()
            self.status_bar.showMessage("AI生成已取消")
    
    def on_ai_generation_completed(self, html_code: str):
        """AI生成完成"""
        # 设置预览内容
        self.preview_widget.set_html_content(html_code)

        # 更新预览时长
        self.preview_widget.set_total_duration(self.project_data.duration)

        # 切换到预览标签
        self.canvas_tabs.setCurrentIndex(1)

        # 更新状态栏
        self.status_bar.showMessage("AI代码生成完成 - 可在预览标签中查看效果")

        # 显示成功消息
        msg = QMessageBox(self)
        msg.setWindowTitle("生成完成")
        msg.setText("🎉 AI动画代码生成完成！")
        msg.setInformativeText("您可以在预览标签中查看动画效果，或导出HTML文件。")
        msg.setIcon(QMessageBox.Icon.Information)

        # 添加按钮
        export_btn = msg.addButton("📁 导出HTML", QMessageBox.ButtonRole.ActionRole)
        preview_btn = msg.addButton("🎬 开始预览", QMessageBox.ButtonRole.ActionRole)
        msg.addButton("关闭", QMessageBox.ButtonRole.RejectRole)

        msg.exec()

        # 处理用户选择
        if msg.clickedButton() == export_btn:
            self.export_generated_html(html_code)
        elif msg.clickedButton() == preview_btn:
            self.preview_widget.play_animation()
    
    def on_ai_generation_failed(self, error_message: str):
        """AI生成失败"""
        QMessageBox.critical(self, "生成失败", f"AI代码生成失败：\n{error_message}")
        self.status_bar.showMessage("AI代码生成失败")
    
    def export_generated_html(self, html_code: str):
        """导出生成的HTML"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存HTML文件", f"{self.project_data.name}.html",
            "HTML文件 (*.html)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_code)
                self.status_bar.showMessage(f"HTML文件已保存: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存文件时出错：{str(e)}")
    
    def new_project(self):
        """新建项目"""
        self.project_data = ProjectData("新项目")
        self.edit_canvas.scene.clear()
        self.timeline.tracks.clear()
        self.asset_panel.assets.clear()
        self.asset_panel.asset_list.clear()
        self.current_element = None
        self.property_panel.set_current_element(None)
        self.status_bar.showMessage("新项目已创建")
    
    def open_project(self):
        """打开项目 - 完整加载所有数据"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目文件", "", "JSON文件 (*.json)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 验证项目文件格式
                if not self.validate_project_data(data):
                    QMessageBox.warning(self, "警告", "项目文件格式不正确")
                    return

                # 清空当前项目
                self.new_project()

                # 重构项目数据
                self.load_project_data(data)

                # 重新加载素材
                self.reload_assets()

                # 重新加载元素到画布
                self.reload_elements_to_canvas()

                self.status_bar.showMessage(f"项目已打开: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "打开失败", f"打开项目文件时出错：{str(e)}")

    def validate_project_data(self, data: dict) -> bool:
        """验证项目数据格式"""
        required_fields = ['name', 'canvas_width', 'canvas_height', 'duration', 'fps']
        return all(field in data for field in required_fields)

    def load_project_data(self, data: dict):
        """加载项目数据"""
        # 基本信息
        self.project_data.name = data.get('name', '未命名项目')
        self.project_data.canvas_width = data.get('canvas_width', 1920)
        self.project_data.canvas_height = data.get('canvas_height', 1080)
        self.project_data.duration = data.get('duration', 10.0)
        self.project_data.fps = data.get('fps', 30)

        # 加载素材
        assets_data = data.get('assets', [])
        for asset_data in assets_data:
            try:
                asset = AssetItem(**asset_data)
                self.project_data.assets.append(asset)
            except Exception as e:
                print(f"加载素材失败: {e}")

        # 加载元素
        elements_data = data.get('elements', [])
        for element_data in elements_data:
            try:
                # 重构Position对象
                if 'initial_position' in element_data:
                    pos_data = element_data['initial_position']
                    element_data['initial_position'] = Position(**pos_data)

                # 重构KeyFrame对象
                if 'keyframes' in element_data:
                    keyframes = []
                    for kf_data in element_data['keyframes']:
                        if 'position' in kf_data:
                            kf_data['position'] = Position(**kf_data['position'])
                        keyframes.append(KeyFrame(**kf_data))
                    element_data['keyframes'] = keyframes

                # 重构AnimationSegment对象
                if 'animation_segments' in element_data:
                    segments = []
                    for seg_data in element_data['animation_segments']:
                        if 'start_position' in seg_data:
                            seg_data['start_position'] = Position(**seg_data['start_position'])
                        if 'end_position' in seg_data:
                            seg_data['end_position'] = Position(**seg_data['end_position'])
                        if 'trajectory_path' in seg_data and seg_data['trajectory_path']:
                            trajectory = [Position(**pos) for pos in seg_data['trajectory_path']]
                            seg_data['trajectory_path'] = trajectory
                        segments.append(AnimationSegment(**seg_data))
                    element_data['animation_segments'] = segments

                element = StageElement(**element_data)
                self.project_data.elements.append(element)

            except Exception as e:
                print(f"加载元素失败: {e}")

        # 加载AI配置
        if 'ai_config' in data:
            self.project_data.ai_config.update(data['ai_config'])

    def reload_assets(self):
        """重新加载素材到面板"""
        self.asset_panel.assets = self.project_data.assets.copy()
        self.asset_panel.asset_list.clear()

        for asset in self.project_data.assets:
            # 检查文件是否仍然存在
            if os.path.exists(asset.file_path):
                item = QListWidgetItem(asset.name)
                item.setData(Qt.ItemDataRole.UserRole, asset)
                self.asset_panel.asset_list.addItem(item)
            else:
                print(f"素材文件不存在: {asset.file_path}")

    def reload_elements_to_canvas(self):
        """重新加载元素到画布"""
        for element in self.project_data.elements:
            # 查找对应的素材
            asset = next((a for a in self.project_data.assets if a.id == element.asset_id), None)
            if asset and os.path.exists(asset.file_path):
                try:
                    pixmap = self.memory_manager.optimize_pixmap(asset.file_path)
                    self.edit_canvas.add_element(element, pixmap)
                    self.timeline.add_track(element)
                except Exception as e:
                    print(f"加载元素到画布失败: {e}")

    def save_project(self):
        """保存项目 - 完整保存所有数据"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存项目文件", f"{self.project_data.name}.json",
            "JSON文件 (*.json)"
        )

        if file_path:
            try:
                # 准备保存数据
                save_data = self.prepare_save_data()

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, indent=2, ensure_ascii=False)

                self.status_bar.showMessage(f"项目已保存: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存项目文件时出错：{str(e)}")

    def prepare_save_data(self) -> dict:
        """准备保存数据 - 确保所有数据都能正确序列化"""
        # 更新项目数据中的元素（从画布获取最新状态）
        self.update_project_elements_from_canvas()

        # 转换为字典
        data = asdict(self.project_data)

        # 添加版本信息和时间戳
        data['version'] = '1.0'
        data['saved_at'] = datetime.now().isoformat()

        return data

    def update_project_elements_from_canvas(self):
        """从画布更新项目元素数据"""
        for element in self.project_data.elements:
            if element.id in self.edit_canvas.elements:
                canvas_item = self.edit_canvas.elements[element.id]
                # 更新当前位置
                current_pos = canvas_item.pos()
                element.initial_position = Position(current_pos.x(), current_pos.y())
    
    def export_html(self):
        """导出HTML"""
        if hasattr(self, 'preview_widget') and self.preview_widget.html_content:
            self.export_generated_html(self.preview_widget.html_content)
        else:
            QMessageBox.information(self, "提示", "请先生成动画代码")
    
    def play_animation(self):
        """播放动画"""
        self.edit_canvas.start_preview_animation(self.project_data.duration)
        self.status_bar.showMessage("播放动画")

    def pause_animation(self):
        """暂停动画"""
        self.edit_canvas.stop_preview_animation()
        self.status_bar.showMessage("暂停动画")

    def stop_animation(self):
        """停止动画"""
        self.edit_canvas.stop_preview_animation()
        self.time_slider.setValue(0)
        self.timeline.set_current_time(0)
        self.status_bar.showMessage("停止动画")
    
    def toggle_play_pause(self):
        """切换播放/暂停"""
        if hasattr(self.preview_widget, 'is_playing') and self.preview_widget.is_playing:
            self.preview_widget.pause_animation()
        else:
            self.preview_widget.play_animation()

    def quick_add_animation_marker(self):
        """快速添加动画标记"""
        if not self.current_element:
            QMessageBox.information(self, "提示", "请先选择一个元素")
            return

        # 获取当前时间
        current_time = self.timeline.current_time

        # 创建简单的关键帧
        keyframe = KeyFrame(
            time=current_time,
            frame_type="custom",
            description=f"标记点 {current_time:.1f}s",
            position=self.current_element.initial_position
        )

        self.current_element.keyframes.append(keyframe)
        self.property_panel.update_ui()
        self.status_bar.showMessage(f"已在 {current_time:.1f}s 添加动画标记")

    def delete_selected_element(self):
        """删除选中的元素"""
        if not self.current_element:
            QMessageBox.information(self, "提示", "请先选择一个元素")
            return

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除元素 {self.current_element.id} 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 从画布移除
            self.edit_canvas.remove_element(self.current_element.id)

            # 从时间轴移除
            self.timeline.remove_track(self.current_element.id)

            # 从项目数据移除
            self.project_data.elements = [
                e for e in self.project_data.elements
                if e.id != self.current_element.id
            ]

            # 清除当前选择
            self.current_element = None
            self.property_panel.set_current_element(None)

            self.status_bar.showMessage("元素已删除")

    def duplicate_selected_element(self):
        """复制选中的元素"""
        if not self.current_element:
            QMessageBox.information(self, "提示", "请先选择一个元素")
            return

        # 创建新元素ID
        new_element_id = f"element_{len(self.project_data.elements)}"

        # 复制元素数据
        import copy
        new_element = copy.deepcopy(self.current_element)
        new_element.id = new_element_id
        new_element.initial_position = Position(
            self.current_element.initial_position.x + 50,
            self.current_element.initial_position.y + 50
        )

        # 添加到项目
        self.project_data.elements.append(new_element)

        # 找到对应的素材
        asset = next((a for a in self.project_data.assets if a.id == new_element.asset_id), None)
        if asset:
            # 添加到画布
            pixmap = QPixmap(asset.file_path)
            if pixmap.width() > 200 or pixmap.height() > 200:
                pixmap = pixmap.scaled(200, 200, Qt.AspectRatioMode.KeepAspectRatio)

            self.edit_canvas.add_element(new_element, pixmap)

            # 添加到时间轴
            self.timeline.add_track(new_element)

            # 设置为当前元素
            self.current_element = new_element
            self.property_panel.set_current_element(new_element)

            self.status_bar.showMessage(f"已复制元素: {new_element_id}")

    def refresh_preview(self):
        """刷新预览"""
        if hasattr(self.preview_widget, 'refresh_preview'):
            self.preview_widget.refresh_preview()
            self.status_bar.showMessage("预览已刷新")
    
    def on_time_changed(self, value: int):
        """时间改变事件"""
        time_seconds = value / 10.0  # 滑块值转换为秒
        self.time_label.setText(f"{time_seconds:.1f}s")
        self.timeline.set_current_time(time_seconds)
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于AI动画工作室",
            """AI动画工作室 v1.0

一个革命性的动画制作工具，通过自然语言描述
和AI技术简化动画创作流程。

主要特性：
• 双画布设计（编辑+预览）
• 直观的时间轴编辑
• 自然语言动画描述
• 多AI服务集成
• HTML动画导出

作者：AI动画工作室开发团队"""
        )


class AIConfigDialog(QDialog):
    """AI服务配置对话框"""
    
    def __init__(self, current_config: dict, parent=None):
        super().__init__(parent)
        self.current_config = current_config.copy()
        self.setWindowTitle("AI服务配置")
        self.setModal(True)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)

        # OpenAI配置
        openai_group = QGroupBox("OpenAI")
        openai_layout = QFormLayout(openai_group)
        
        self.openai_key_edit = QLineEdit()
        self.openai_key_edit.setText(self.current_config.get("openai_api_key", ""))
        self.openai_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        openai_layout.addRow("API Key:", self.openai_key_edit)
        
        # Claude配置
        claude_group = QGroupBox("Claude")
        claude_layout = QFormLayout(claude_group)
        
        self.claude_key_edit = QLineEdit()
        self.claude_key_edit.setText(self.current_config.get("claude_api_key", ""))
        self.claude_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        claude_layout.addRow("API Key:", self.claude_key_edit)
        
        # Gemini配置
        gemini_group = QGroupBox("Gemini")
        gemini_layout = QFormLayout(gemini_group)
        
        self.gemini_key_edit = QLineEdit()
        self.gemini_key_edit.setText(self.current_config.get("gemini_api_key", ""))
        self.gemini_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        gemini_layout.addRow("API Key:", self.gemini_key_edit)
        
        # 偏好设置
        pref_group = QGroupBox("偏好设置")
        pref_layout = QFormLayout(pref_group)
        
        self.preferred_service_combo = QComboBox()
        self.preferred_service_combo.addItems(["openai", "claude", "gemini"])
        current_service = self.current_config.get("preferred_service", "openai")
        index = self.preferred_service_combo.findText(current_service)
        if index >= 0:
            self.preferred_service_combo.setCurrentIndex(index)
        pref_layout.addRow("首选服务:", self.preferred_service_combo)
        
        layout.addWidget(openai_group)
        layout.addWidget(claude_group)
        layout.addWidget(gemini_group)
        layout.addWidget(pref_group)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def get_config(self) -> dict:
        """获取配置数据"""
        return {
            "openai_api_key": self.openai_key_edit.text(),
            "claude_api_key": self.claude_key_edit.text(),
            "gemini_api_key": self.gemini_key_edit.text(),
            "preferred_service": self.preferred_service_combo.currentText()
        }


# =============================================================================
# 应用程序入口
# =============================================================================

def main():
    """程序主入口"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("AI动画工作室")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("AI Animation Studio")
    
    # 设置暗色主题
    app.setStyle("Fusion")
    palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.WindowText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Base, QColor(25, 25, 25))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(0, 0, 0))
    palette.setColor(QPalette.ColorRole.ToolTipText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Text, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor(255, 255, 255))
    palette.setColor(QPalette.ColorRole.BrightText, QColor(255, 0, 0))
    palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor(0, 0, 0))
    app.setPalette(palette)
    
    # 创建主窗口
    window = AIAnimationStudio()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()